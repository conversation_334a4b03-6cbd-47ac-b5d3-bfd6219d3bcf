package com.ym.synapse

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.*
import com.ym.synapse.ui.theme.SynapseTheme

class TagManagementActivity : ComponentActivity() {
    
    companion object {
        const val EXTRA_SELECTED_TAG = "selected_tag"
        
        fun createIntent(context: Context, selectedTag: String? = null): Intent {
            return Intent(context, TagManagementActivity::class.java).apply {
                if (selectedTag != null) {
                    putExtra(EXTRA_SELECTED_TAG, selectedTag)
                }
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val selectedTag = intent.getStringExtra(EXTRA_SELECTED_TAG)
        
        setContent {
            SynapseTheme {
                TagManagementScreen(
                    onBackClick = { finish() },
                    selectedTag = selectedTag
                )
            }
        }
    }
}
