package com.ym.synapse

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.ym.synapse.config.ModelConfigDetailScreen
import com.ym.synapse.ui.theme.SynapseTheme

class ModelConfigActivity : ComponentActivity() {
    
    companion object {
        fun createIntent(context: Context): Intent {
            return Intent(context, ModelConfigActivity::class.java)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        setContent {
            SynapseTheme {
                ModelConfigDetailScreen(
                    onBackClick = { finish() }
                )
            }
        }
    }
}
