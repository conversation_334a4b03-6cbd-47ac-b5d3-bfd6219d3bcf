package com.ym.synapse

import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.ym.synapse.data.TagManager
import kotlinx.coroutines.launch

/**
 * 标签管理界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TagManagementScreen(
    onBackClick: () -> Unit,
    selectedTag: String? = null
) {
    val context = LocalContext.current
    val tagManager = TagManager.getInstance(context)
    val coroutineScope = rememberCoroutineScope()

    var tagsStatistics by remember { mutableStateOf(tagManager.getTagsStatistics()) }
    var showCleanupDialog by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var tagToDelete by remember { mutableStateOf("") }
    var showEditDialog by remember { mutableStateOf(false) }
    var tagToEdit by remember { mutableStateOf("") }
    var editTagText by remember { mutableStateOf("") }

    // 标签定位相关状态
    var highlightedTag by remember { mutableStateOf(selectedTag) }

    // 如果有选中的标签，显示提示并在3秒后清除高亮
    LaunchedEffect(selectedTag) {
        if (selectedTag != null) {
            kotlinx.coroutines.delay(3000)
            highlightedTag = null
        }
    }

    // 刷新数据的函数
    val refreshData = {
        tagsStatistics = tagManager.getTagsStatistics()
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("标签管理") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    TextButton(
                        onClick = { showCleanupDialog = true }
                    ) {
                        Text("清理")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 统计信息
            item {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "标签统计",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text("总标签数: ${tagsStatistics.size}")
                        Text("总使用次数: ${tagsStatistics.values.sum()}")
                        
                        if (tagsStatistics.isNotEmpty()) {
                            val mostUsedTag = tagsStatistics.maxByOrNull { it.value }
                            Text("最常用标签: ${mostUsedTag?.key} (${mostUsedTag?.value}次)")
                        }
                    }
                }
            }
            
            // 按图片类型分组显示标签
            item {
                Text(
                    text = "各类型常用标签",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            // Text-Heavy 标签
            item {
                TagTypeSection(
                    title = "文字密集型 (Text-Heavy)",
                    tags = tagManager.getTagsForImageType("Text-Heavy"),
                    onRefresh = refreshData
                )
            }
            
            // Rich-Content 标签
            item {
                TagTypeSection(
                    title = "富内容型 (Rich-Content)",
                    tags = tagManager.getTagsForImageType("Rich-Content"),
                    onRefresh = refreshData
                )
            }
            
            // Simple-Image 标签
            item {
                TagTypeSection(
                    title = "简单图片型 (Simple-Image)",
                    tags = tagManager.getTagsForImageType("Simple-Image"),
                    onRefresh = refreshData
                )
            }
            
            // 所有标签列表
            item {
                Text(
                    text = "所有标签 (按使用频率排序)",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            items(tagsStatistics.toList().sortedByDescending { it.second }) { (tag, count) ->
                TagItem(
                    tag = tag,
                    count = count,
                    isHighlighted = highlightedTag == tag,
                    onEditClick = {
                        tagToEdit = tag
                        editTagText = tag
                        showEditDialog = true
                    },
                    onDeleteClick = {
                        tagToDelete = tag
                        showDeleteDialog = true
                    }
                )
            }
        }
    }
    
    // 清理对话框
    if (showCleanupDialog) {
        AlertDialog(
            onDismissRequest = { showCleanupDialog = false },
            title = { Text("清理低频标签") },
            text = { 
                Text("是否要清理使用次数少于2次的标签？这将有助于保持标签库的整洁。")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        coroutineScope.launch {
                            try {
                                tagManager.cleanupLowFrequencyTags(2)
                                refreshData()
                                Toast.makeText(context, "已清理低频标签", Toast.LENGTH_SHORT).show()
                            } catch (e: Exception) {
                                Toast.makeText(context, "清理失败: ${e.message}", Toast.LENGTH_SHORT).show()
                            }
                            showCleanupDialog = false
                        }
                    }
                ) {
                    Text("确认清理")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showCleanupDialog = false }
                ) {
                    Text("取消")
                }
            }
        )
    }

    // 删除标签对话框
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text("删除标签") },
            text = {
                Text("确定要删除标签 \"$tagToDelete\" 吗？此操作不可撤销。")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        coroutineScope.launch {
                            try {
                                tagManager.deleteTag(tagToDelete)
                                refreshData()
                                Toast.makeText(context, "标签已删除", Toast.LENGTH_SHORT).show()
                            } catch (e: Exception) {
                                Toast.makeText(context, "删除失败: ${e.message}", Toast.LENGTH_SHORT).show()
                            }
                            showDeleteDialog = false
                        }
                    }
                ) {
                    Text("删除", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteDialog = false }
                ) {
                    Text("取消")
                }
            }
        )
    }

    // 编辑标签对话框
    if (showEditDialog) {
        AlertDialog(
            onDismissRequest = { showEditDialog = false },
            title = { Text("编辑标签") },
            text = {
                Column {
                    Text("修改标签名称：")
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = editTagText,
                        onValueChange = { editTagText = it },
                        label = { Text("标签名称") },
                        singleLine = true
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (editTagText.trim().isNotEmpty() && editTagText.trim() != tagToEdit) {
                            coroutineScope.launch {
                                try {
                                    tagManager.renameTag(tagToEdit, editTagText.trim())
                                    refreshData()
                                    Toast.makeText(context, "标签已更新", Toast.LENGTH_SHORT).show()
                                } catch (e: Exception) {
                                    Toast.makeText(context, "更新失败: ${e.message}", Toast.LENGTH_SHORT).show()
                                }
                                showEditDialog = false
                            }
                        } else {
                            showEditDialog = false
                        }
                    }
                ) {
                    Text("保存")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showEditDialog = false }
                ) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 标签类型分组组件
 */
@Composable
fun TagTypeSection(
    title: String,
    tags: List<String>,
    onRefresh: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            if (tags.isEmpty()) {
                Text(
                    text = "暂无标签",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            } else {
                // 显示前5个标签
                tags.take(5).forEach { tag ->
                    Text(
                        text = "• $tag",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(vertical = 2.dp)
                    )
                }
                
                if (tags.size > 5) {
                    Text(
                        text = "... 还有 ${tags.size - 5} 个标签",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 单个标签项组件
 */
@Composable
fun TagItem(
    tag: String,
    count: Int,
    isHighlighted: Boolean = false,
    onEditClick: () -> Unit = {},
    onDeleteClick: () -> Unit = {}
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = if (isHighlighted) {
            CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        } else {
            CardDefaults.cardColors()
        }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = tag,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "使用 $count 次",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 使用频率指示器
            val frequencyColor = when {
                count >= 10 -> MaterialTheme.colorScheme.primary
                count >= 5 -> MaterialTheme.colorScheme.secondary
                else -> MaterialTheme.colorScheme.tertiary
            }
            
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Surface(
                    color = frequencyColor,
                    shape = MaterialTheme.shapes.small,
                    modifier = Modifier.padding(end = 8.dp)
                ) {
                    Text(
                        text = when {
                            count >= 10 -> "高频"
                            count >= 5 -> "中频"
                            else -> "低频"
                        },
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onPrimary,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }

                // 编辑按钮
                IconButton(
                    onClick = onEditClick,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "编辑标签",
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                }

                // 删除按钮
                IconButton(
                    onClick = onDeleteClick,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "删除标签",
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.error
                    )
                }
            }
        }
    }
}
