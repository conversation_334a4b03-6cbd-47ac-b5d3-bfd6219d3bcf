package com.ym.synapse

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.ym.synapse.config.ApiConfigDetailScreen
import com.ym.synapse.ui.theme.SynapseTheme

class ApiConfigActivity : ComponentActivity() {

    companion object {
        fun createIntent(context: Context): Intent {
            return Intent(context, ApiConfigActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            SynapseTheme {
                ApiConfigDetailScreen(
                    onBackClick = { finish() }
                )
            }
        }
    }
}
