package com.ym.synapse

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import com.ym.synapse.ui.screens.MainScreen
import com.ym.synapse.ui.theme.SynapseTheme
import com.ym.synapse.utils.ResourceManager

/**
 * 重构后的MainActivity
 * 使用模块化架构，职责更加清晰
 */
class MainActivity : ComponentActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            SynapseTheme {
                // 使用迁移后的MainScreen组件
                MainScreen()
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理资源
        ResourceManager.cleanupAllResources()
    }
}

// 原有的MVVM架构组件已迁移到独立的MainScreen组件中
// 这里保留简化的结构，便于后续扩展
