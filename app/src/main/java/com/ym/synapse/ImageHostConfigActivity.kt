package com.ym.synapse

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.ym.synapse.config.ImageHostConfigDetailScreen
import com.ym.synapse.ui.theme.SynapseTheme

class ImageHostConfigActivity : ComponentActivity() {
    
    companion object {
        fun createIntent(context: Context): Intent {
            return Intent(context, ImageHostConfigActivity::class.java)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        setContent {
            SynapseTheme {
                ImageHostConfigDetailScreen(
                    onBackClick = { finish() }
                )
            }
        }
    }
}
