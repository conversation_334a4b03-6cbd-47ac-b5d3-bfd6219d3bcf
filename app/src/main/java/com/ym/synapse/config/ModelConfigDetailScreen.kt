package com.ym.synapse.config

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.ym.synapse.*
import com.ym.synapse.data.ModelConfigManager
import com.ym.synapse.data.ImageTypeConfig

/**
 * 模型配置详细界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ModelConfigDetailScreen(
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    val sharedPreferences = remember {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    var modelConfigMode by remember { mutableStateOf(ModelConfigManager.getModelConfigMode(context)) }
    var imageTypeConfigs by remember { mutableStateOf(ModelConfigManager.getAllImageTypeConfigs(context)) }

    // 共用模式的配置
    var sharedApiUrl by remember { mutableStateOf(sharedPreferences.getString(KEY_API_URL, "") ?: "") }
    var sharedApiKey by remember { mutableStateOf(sharedPreferences.getString(KEY_API_KEY, "") ?: "") }
    var sharedModelId by remember { mutableStateOf(sharedPreferences.getString(KEY_AI_OCR_MODEL_ID, DEFAULT_AI_OCR_MODEL_ID) ?: DEFAULT_AI_OCR_MODEL_ID) }
    var sharedPrompt by remember { mutableStateOf(sharedPreferences.getString(KEY_AI_OCR_PROMPT, DEFAULT_AI_OCR_PROMPT) ?: DEFAULT_AI_OCR_PROMPT) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("模型配置") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 说明卡片
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "模型配置说明",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "配置不同图片类型使用的AI模型。可以选择共用一个模型或为每种类型配置专用模型。",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            // 配置模式选择
            item {
                Text(
                    text = "配置模式",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }

            item {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        // 共用模式
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = modelConfigMode == MODEL_CONFIG_SHARED,
                                onClick = {
                                    modelConfigMode = MODEL_CONFIG_SHARED
                                    ModelConfigManager.setModelConfigMode(context, MODEL_CONFIG_SHARED)
                                }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Column {
                                Text(
                                    text = "共用一个模型",
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Medium
                                )
                                Text(
                                    text = "所有图片类型使用相同的API配置",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        // 分离模式
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = modelConfigMode == MODEL_CONFIG_SEPARATE,
                                onClick = {
                                    modelConfigMode = MODEL_CONFIG_SEPARATE
                                    ModelConfigManager.setModelConfigMode(context, MODEL_CONFIG_SEPARATE)
                                }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Column {
                                Text(
                                    text = "每个类型不同模型",
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Medium
                                )
                                Text(
                                    text = "为每种图片类型配置专用的API",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                }
            }

            // 根据模式显示不同的配置界面
            if (modelConfigMode == MODEL_CONFIG_SHARED) {
                // 共用模式配置
                item {
                    Text(
                        text = "共用模型配置",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                }

                item {
                    SharedModelConfigCard(
                        apiUrl = sharedApiUrl,
                        apiKey = sharedApiKey,
                        modelId = sharedModelId,
                        prompt = sharedPrompt,
                        onApiUrlChange = {
                            sharedApiUrl = it
                            sharedPreferences.edit().putString(KEY_API_URL, it).apply()
                        },
                        onApiKeyChange = {
                            sharedApiKey = it
                            sharedPreferences.edit().putString(KEY_API_KEY, it).apply()
                        },
                        onModelIdChange = {
                            sharedModelId = it
                            sharedPreferences.edit().putString(KEY_AI_OCR_MODEL_ID, it).apply()
                        },
                        onPromptChange = {
                            sharedPrompt = it
                            sharedPreferences.edit().putString(KEY_AI_OCR_PROMPT, it).apply()
                        },
                        onSave = { /* 不再需要保存按钮 */ }
                    )
                }
            } else {
                // 分离模式配置
                item {
                    Text(
                        text = "各类型模型配置",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                }

                items(imageTypeConfigs) { config ->
                    ImageTypeConfigCard(
                        config = config,
                        onConfigChange = { updatedConfig ->
                            ModelConfigManager.saveImageTypeConfig(context, updatedConfig)
                            imageTypeConfigs = ModelConfigManager.getAllImageTypeConfigs(context)
                        }
                    )
                }
            }
        }
    }
}

/**
 * 共用模型配置卡片
 */
@Composable
fun SharedModelConfigCard(
    apiUrl: String,
    apiKey: String,
    modelId: String,
    prompt: String,
    onApiUrlChange: (String) -> Unit,
    onApiKeyChange: (String) -> Unit,
    onModelIdChange: (String) -> Unit,
    onPromptChange: (String) -> Unit,
    onSave: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            OutlinedTextField(
                value = apiUrl,
                onValueChange = onApiUrlChange,
                label = { Text("API URL") },
                modifier = Modifier.fillMaxWidth(),
                supportingText = { Text("自动保存") }
            )

            OutlinedTextField(
                value = apiKey,
                onValueChange = onApiKeyChange,
                label = { Text("API Key") },
                modifier = Modifier.fillMaxWidth(),
                supportingText = { Text("自动保存") }
            )

            OutlinedTextField(
                value = modelId,
                onValueChange = onModelIdChange,
                label = { Text("模型ID") },
                modifier = Modifier.fillMaxWidth(),

            )

            OutlinedTextField(
                value = prompt,
                onValueChange = onPromptChange,
                label = { Text("提示词") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 3,
                maxLines = 5,

            )


        }
    }
}

/**
 * 图片类型配置卡片
 */
@Composable
fun ImageTypeConfigCard(
    config: ImageTypeConfig,
    onConfigChange: (ImageTypeConfig) -> Unit
) {
    var apiUrl by remember { mutableStateOf(config.apiUrl) }
    var apiKey by remember { mutableStateOf(config.apiKey) }
    var modelId by remember { mutableStateOf(config.modelId) }
    var prompt by remember { mutableStateOf(config.prompt) }

    // 自动保存函数
    val autoSave = { field: String, value: String ->
        val updatedConfig = when (field) {
            "apiUrl" -> config.copy(apiUrl = value, apiKey = apiKey, modelId = modelId, prompt = prompt)
            "apiKey" -> config.copy(apiUrl = apiUrl, apiKey = value, modelId = modelId, prompt = prompt)
            "modelId" -> config.copy(apiUrl = apiUrl, apiKey = apiKey, modelId = value, prompt = prompt)
            "prompt" -> config.copy(apiUrl = apiUrl, apiKey = apiKey, modelId = modelId, prompt = value)
            else -> config
        }
        onConfigChange(updatedConfig)
    }

    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = config.displayName,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            OutlinedTextField(
                value = apiUrl,
                onValueChange = {
                    apiUrl = it
                    autoSave("apiUrl", it)
                },
                label = { Text("API URL") },
                modifier = Modifier.fillMaxWidth(),
                supportingText = { Text("自动保存") }
            )

            OutlinedTextField(
                value = apiKey,
                onValueChange = {
                    apiKey = it
                    autoSave("apiKey", it)
                },
                label = { Text("API Key") },
                modifier = Modifier.fillMaxWidth(),
                supportingText = { Text("自动保存") }
            )

            OutlinedTextField(
                value = modelId,
                onValueChange = {
                    modelId = it
                    autoSave("modelId", it)
                },
                label = { Text("模型ID") },
                modifier = Modifier.fillMaxWidth(),

            )

            OutlinedTextField(
                value = prompt,
                onValueChange = {
                    prompt = it
                    autoSave("prompt", it)
                },
                label = { Text("提示词") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 3,
                maxLines = 5,

            )


        }
    }
}
