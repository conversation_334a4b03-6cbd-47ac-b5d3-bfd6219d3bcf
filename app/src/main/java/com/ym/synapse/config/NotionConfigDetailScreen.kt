package com.ym.synapse.config

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.ym.synapse.*
import com.ym.synapse.service.NotionService
import com.ym.synapse.service.NotionResult
import kotlinx.coroutines.launch

/**
 * Notion配置详细界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotionConfigDetailScreen(
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val sharedPreferences = remember {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    var notionToken by remember { mutableStateOf(sharedPreferences.getString(KEY_NOTION_TOKEN, "") ?: "") }
    var notionDatabaseId by remember { mutableStateOf(sharedPreferences.getString(KEY_NOTION_DATABASE_ID, "") ?: "") }
    var notionEnabled by remember { mutableStateOf(sharedPreferences.getBoolean(KEY_NOTION_ENABLED, false)) }
    var notionAutoSync by remember { mutableStateOf(sharedPreferences.getBoolean(KEY_NOTION_AUTO_SYNC, false)) }

    // 自动保存函数
    val autoSave = { key: String, value: Any ->
        with(sharedPreferences.edit()) {
            when (value) {
                is String -> putString(key, value)
                is Boolean -> putBoolean(key, value)
            }
            apply()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Notion集成") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 说明卡片
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Notion集成配置",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "将AI分析结果自动发送到Notion数据库，构建个人知识库。需要先创建Notion Integration和数据库。",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }

            // 启用开关
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "启用Notion集成",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "将分析结果自动发送到Notion数据库",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    Switch(
                        checked = notionEnabled,
                        onCheckedChange = {
                            notionEnabled = it
                            autoSave(KEY_NOTION_ENABLED, it)
                        }
                    )
                }
            }

            if (notionEnabled) {
                // Integration Token配置
                OutlinedTextField(
                    value = notionToken,
                    onValueChange = {
                        notionToken = it
                        autoSave(KEY_NOTION_TOKEN, it)
                    },
                    label = { Text("Integration Token") },
                    placeholder = { Text("secret_...") },
                    modifier = Modifier.fillMaxWidth(),
                    supportingText = {
                        Text("从Notion Developers页面获取的Integration Token")
                    }
                )

                // Database ID配置
                OutlinedTextField(
                    value = notionDatabaseId,
                    onValueChange = {
                        notionDatabaseId = it
                        autoSave(KEY_NOTION_DATABASE_ID, it)
                    },
                    label = { Text("Database ID") },
                    placeholder = { Text("32位字符串") },
                    modifier = Modifier.fillMaxWidth(),
                    supportingText = {
                        Text("Notion数据库的唯一标识符")
                    }
                )

                // 自动同步开关
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "自动同步",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = "截图分析完成后自动发送到Notion",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        Switch(
                            checked = notionAutoSync,
                            onCheckedChange = {
                                notionAutoSync = it
                                autoSave(KEY_NOTION_AUTO_SYNC, it)
                            }
                        )
                    }
                }

                // 测试连接按钮
                OutlinedButton(
                    onClick = {
                        coroutineScope.launch {
                            // 测试连接（配置已自动保存）
                            val result = NotionService.getInstance().testConnection(context)
                            val message = when (result) {
                                is NotionResult.Success -> "Notion连接成功！"
                                is NotionResult.Error -> "连接失败: ${result.message}"
                            }
                            android.widget.Toast.makeText(context, message, android.widget.Toast.LENGTH_LONG).show()
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = notionToken.isNotEmpty() && notionDatabaseId.isNotEmpty()
                ) {
                    Text("🧪 测试连接")
                }
            }
        }
    }
}
