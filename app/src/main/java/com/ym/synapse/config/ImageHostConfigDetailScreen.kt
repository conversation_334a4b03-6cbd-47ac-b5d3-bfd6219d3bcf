package com.ym.synapse.config

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.ym.synapse.*
import com.ym.synapse.service.ImageHostService
import com.ym.synapse.api.ImageHostResult
import kotlinx.coroutines.launch

/**
 * 图床配置详细界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ImageHostConfigDetailScreen(
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val sharedPreferences = remember {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    var imageHostEnabled by remember { mutableStateOf(sharedPreferences.getBoolean(KEY_IMAGE_HOST_ENABLED, false)) }
    var imageHostService by remember { mutableStateOf(sharedPreferences.getString(KEY_IMAGE_HOST_SERVICE, IMAGE_HOST_NONE) ?: IMAGE_HOST_NONE) }
    var imgbbApiKey by remember { mutableStateOf(sharedPreferences.getString(KEY_IMGBB_API_KEY, "") ?: "") }
    var smmsApiKey by remember { mutableStateOf(sharedPreferences.getString(KEY_SMMS_API_KEY, "") ?: "") }

    // 自动保存函数
    val autoSave = { key: String, value: Any ->
        with(sharedPreferences.edit()) {
            when (value) {
                is String -> putString(key, value)
                is Boolean -> putBoolean(key, value)
            }
            apply()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("图床服务") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 说明卡片
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "图床服务配置",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "将截图上传到图床服务，在Notion中显示图片。支持ImgBB和SM.MS两种免费图床服务。",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }

            // 启用开关
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "启用图床服务",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "将截图上传到图床，在Notion中显示图片",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    Switch(
                        checked = imageHostEnabled,
                        onCheckedChange = {
                            imageHostEnabled = it
                            autoSave(KEY_IMAGE_HOST_ENABLED, it)
                        }
                    )
                }
            }

            if (imageHostEnabled) {
                // 服务选择
                Text(
                    text = "选择图床服务",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )

                // ImgBB选项
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = imageHostService == IMAGE_HOST_IMGBB,
                            onClick = {
                                imageHostService = IMAGE_HOST_IMGBB
                                autoSave(KEY_IMAGE_HOST_SERVICE, IMAGE_HOST_IMGBB)
                            }
                        )
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "ImgBB (推荐)",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = "免费、稳定、每月1000次上传",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }

                // SM.MS选项
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = imageHostService == IMAGE_HOST_SMMS,
                            onClick = {
                                imageHostService = IMAGE_HOST_SMMS
                                autoSave(KEY_IMAGE_HOST_SERVICE, IMAGE_HOST_SMMS)
                            }
                        )
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "SM.MS",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = "完全免费、无限制、单文件5MB",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }

                // API Key输入
                when (imageHostService) {
                    IMAGE_HOST_IMGBB -> {
                        OutlinedTextField(
                            value = imgbbApiKey,
                            onValueChange = {
                                imgbbApiKey = it
                                autoSave(KEY_IMGBB_API_KEY, it)
                            },
                            label = { Text("ImgBB API Key") },
                            placeholder = { Text("获取地址: https://api.imgbb.com/") },
                            modifier = Modifier.fillMaxWidth(),
                            supportingText = {
                                Text("从ImgBB官网获取的API密钥（自动保存）")
                            }
                        )
                    }
                    IMAGE_HOST_SMMS -> {
                        OutlinedTextField(
                            value = smmsApiKey,
                            onValueChange = {
                                smmsApiKey = it
                                autoSave(KEY_SMMS_API_KEY, it)
                            },
                            label = { Text("SM.MS API Key (可选)") },
                            placeholder = { Text("不填写则使用游客模式") },
                            modifier = Modifier.fillMaxWidth(),
                            supportingText = {
                                Text("可选：注册用户可获得更高的上传限制（自动保存）")
                            }
                        )
                    }
                }

                // 测试连接按钮
                OutlinedButton(
                    onClick = {
                        coroutineScope.launch {
                            // 测试连接（配置已自动保存）
                            val result = ImageHostService.getInstance().testConnection(context)
                            val message = when (result) {
                                is ImageHostResult.Success -> "图床服务配置成功！"
                                is ImageHostResult.Error -> "配置失败: ${result.message}"
                            }
                            android.widget.Toast.makeText(context, message, android.widget.Toast.LENGTH_LONG).show()
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = imageHostService != IMAGE_HOST_NONE &&
                             (imageHostService != IMAGE_HOST_IMGBB || imgbbApiKey.isNotEmpty())
                ) {
                    Text("🧪 测试图床连接")
                }
            }




        }
    }
}
