package com.ym.synapse.config

import android.content.Context
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import com.ym.synapse.*
import com.ym.synapse.ui.animations.PredictiveBackContainer

/**
 * API配置主界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ApiConfigDetailScreen(
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    var currentScreen by remember { mutableStateOf("main") }
    var selectedApiType by remember { mutableStateOf("") }

    // 处理系统返回键 - 这应该支持预测性返回
    androidx.activity.compose.BackHandler(enabled = currentScreen == "edit") {
        android.util.Log.d("ApiConfigDetailScreen", "*** BACK HANDLER TRIGGERED *** from edit to main")
        currentScreen = "main"
    }

    // 使用AnimatedContent实现原生滑动动画
    AnimatedContent(
        targetState = currentScreen,
        transitionSpec = {
            if (targetState == "edit") {
                // 进入编辑页面：从右侧滑入
                slideInHorizontally(
                    initialOffsetX = { fullWidth -> fullWidth },
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                ) + fadeIn(
                    animationSpec = tween(300)
                ) togetherWith slideOutHorizontally(
                    targetOffsetX = { fullWidth -> -fullWidth },
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                ) + fadeOut(
                    animationSpec = tween(300)
                )
            } else {
                // 返回主页面：从左侧滑入
                slideInHorizontally(
                    initialOffsetX = { fullWidth -> -fullWidth },
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                ) + fadeIn(
                    animationSpec = tween(300)
                ) togetherWith slideOutHorizontally(
                    targetOffsetX = { fullWidth -> fullWidth },
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                ) + fadeOut(
                    animationSpec = tween(300)
                )
            }
        },
        label = "api_config_navigation"
    ) { screen ->
        when (screen) {
            "main" -> ApiConfigMainScreen(
                onBackClick = onBackClick,
                onApiTypeClick = { apiType ->
                    android.util.Log.d("ApiConfigDetailScreen", "*** NAVIGATING TO EDIT *** apiType=$apiType")
                    selectedApiType = apiType
                    currentScreen = "edit"
                }
            )
            "edit" -> ApiEditScreen(
                apiType = selectedApiType,
                onBackClick = {
                    android.util.Log.d("ApiConfigDetailScreen", "*** NAVIGATING TO MAIN *** from edit")
                    currentScreen = "main"
                }
            )
        }
    }
}

/**
 * API配置主界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ApiConfigMainScreen(
    onBackClick: () -> Unit,
    onApiTypeClick: (String) -> Unit
) {
    val context = LocalContext.current
    val sharedPreferences = remember {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    // 统一API配置开关
    var useUnifiedApi by remember { 
        mutableStateOf(sharedPreferences.getBoolean("use_unified_api", true)) 
    }
    
    // 备用API开关
    var fallbackEnabled by remember { 
        mutableStateOf(sharedPreferences.getBoolean(KEY_FALLBACK_ENABLED, false)) 
    }

    // 自动保存函数
    val autoSaveBoolean = { key: String, value: Boolean ->
        sharedPreferences.edit().putBoolean(key, value).apply()
    }

    // API类型列表
    val apiTypes = listOf(
        ApiTypeInfo(
            type = "unified",
            title = "统一API配置",
            description = "所有功能使用相同的API配置",
            icon = "🔗"
        ),
        ApiTypeInfo(
            type = "fallback",
            title = "分流API配置",
            description = "处理分流请求的API配置",
            icon = "🔄"
        ),
        ApiTypeInfo(
            type = IMAGE_TYPE_TEXT_HEAVY,
            title = "文字密集型API",
            description = "处理包含大量文字的图片",
            icon = "📄"
        ),
        ApiTypeInfo(
            type = IMAGE_TYPE_RICH_CONTENT,
            title = "富内容型API",
            description = "处理包含文字和图像混合内容的图片",
            icon = "🖼️"
        ),
        ApiTypeInfo(
            type = IMAGE_TYPE_SIMPLE_IMAGE,
            title = "简单图片型API",
            description = "处理简单图像内容的图片",
            icon = "📸"
        )
    )

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("API配置") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            item {
                // 说明卡片
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "API配置管理",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "配置AI服务的API地址、密钥和模型。可以统一配置或为每个功能单独配置。",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            item {
                // 统一API配置开关
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "统一API配置",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = if (useUnifiedApi) "所有功能使用相同的API配置" else "为每个功能单独配置API",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        Switch(
                            checked = useUnifiedApi,
                            onCheckedChange = {
                                useUnifiedApi = it
                                autoSaveBoolean("use_unified_api", it)
                            }
                        )
                    }
                }
            }

            item {
                // 备用API开关
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "启用备用API",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = "主API失败时自动使用备用API",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        Switch(
                            checked = fallbackEnabled,
                            onCheckedChange = {
                                fallbackEnabled = it
                                autoSaveBoolean(KEY_FALLBACK_ENABLED, it)
                            }
                        )
                    }
                }
            }

            // 显示相应的API配置项
            val visibleApiTypes = if (useUnifiedApi) {
                listOf(apiTypes[0]) // 只显示统一配置
            } else {
                apiTypes.drop(1) // 显示除统一配置外的所有配置（包括分流）
            }

            items(visibleApiTypes) { apiType ->
                ApiTypeCard(
                    apiType = apiType,
                    onClick = { onApiTypeClick(apiType.type) }
                )
            }

            // 如果启用了备用API，显示备用API配置
            if (fallbackEnabled) {
                item {
                    Text(
                        text = "备用API配置",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                }

                val fallbackApiTypes = if (useUnifiedApi) {
                    listOf(
                        ApiTypeInfo(
                            type = "backup_unified",
                            title = "统一备用API",
                            description = "所有功能的备用API配置",
                            icon = "🔗"
                        )
                    )
                } else {
                    listOf(
                        ApiTypeInfo(
                            type = "backup_fallback",
                            title = "分流备用API",
                            description = "分流功能的备用API",
                            icon = "🔄"
                        ),
                        ApiTypeInfo(
                            type = "backup_" + IMAGE_TYPE_TEXT_HEAVY,
                            title = "文字密集型备用API",
                            description = "文字密集型功能的备用API",
                            icon = "📄"
                        ),
                        ApiTypeInfo(
                            type = "backup_" + IMAGE_TYPE_RICH_CONTENT,
                            title = "富内容型备用API",
                            description = "富内容型功能的备用API",
                            icon = "🖼️"
                        ),
                        ApiTypeInfo(
                            type = "backup_" + IMAGE_TYPE_SIMPLE_IMAGE,
                            title = "简单图片型备用API",
                            description = "简单图片型功能的备用API",
                            icon = "📸"
                        )
                    )
                }

                items(fallbackApiTypes) { apiType ->
                    ApiTypeCard(
                        apiType = apiType,
                        onClick = { onApiTypeClick(apiType.type) }
                    )
                }
            }
        }
    }
}

/**
 * API类型卡片
 */
@Composable
fun ApiTypeCard(
    apiType: ApiTypeInfo,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        onClick = onClick
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标
            Text(
                text = apiType.icon,
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(end = 16.dp)
            )

            // 内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = apiType.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = apiType.description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // 编辑图标
            Icon(
                imageVector = Icons.Default.Edit,
                contentDescription = "编辑",
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
}

/**
 * API编辑界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ApiEditScreen(
    apiType: String,
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    val sharedPreferences = remember {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    // 获取配置键名
    val (urlKey, keyKey, modelKey) = getApiConfigKeys(apiType)

    // 获取当前配置
    var apiUrl by remember {
        mutableStateOf(sharedPreferences.getString(urlKey, "") ?: "")
    }
    var apiKey by remember {
        mutableStateOf(sharedPreferences.getString(keyKey, "") ?: "")
    }
    var modelId by remember {
        mutableStateOf(sharedPreferences.getString(modelKey, "") ?: "")
    }

    // 获取标题
    val title = getApiTypeTitle(apiType)

    // 自动保存函数
    val autoSave = { key: String, value: String ->
        sharedPreferences.edit().putString(key, value).apply()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(title) },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 说明文字
            Text(
                text = "配置 $title",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Text(
                text = "修改后会自动保存，无需手动保存",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            // API URL配置
            OutlinedTextField(
                value = apiUrl,
                onValueChange = {
                    apiUrl = it
                    autoSave(urlKey, it)
                },
                label = { Text("API URL") },
                placeholder = { Text("https://api.openai.com/v1/chat/completions") },
                modifier = Modifier.fillMaxWidth(),
                supportingText = {
                    Text("AI服务的API地址，支持OpenAI兼容接口")
                }
            )

            // API Key配置
            OutlinedTextField(
                value = apiKey,
                onValueChange = {
                    apiKey = it
                    autoSave(keyKey, it)
                },
                label = { Text("API Key") },
                placeholder = { Text("sk-...") },
                modifier = Modifier.fillMaxWidth(),
                supportingText = {
                    Text("API密钥，用于身份验证")
                }
            )

            // 模型ID配置
            OutlinedTextField(
                value = modelId,
                onValueChange = {
                    modelId = it
                    autoSave(modelKey, it)
                },
                label = { Text("模型ID") },
                placeholder = { Text("gpt-4-vision-preview") },
                modifier = Modifier.fillMaxWidth(),
                supportingText = {
                    Text("AI模型标识符，需要支持视觉分析功能")
                }
            )

            Spacer(modifier = Modifier.weight(1f))

            // 重置按钮
            OutlinedButton(
                onClick = {
                    apiUrl = ""
                    apiKey = ""
                    modelId = ""
                    with(sharedPreferences.edit()) {
                        putString(urlKey, "")
                        putString(keyKey, "")
                        putString(modelKey, "")
                        apply()
                    }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("重置为空")
            }
        }
    }
}

/**
 * 获取API配置键名
 */
fun getApiConfigKeys(apiType: String): Triple<String, String, String> {
    return when (apiType) {
        "unified" -> Triple(KEY_API_URL, KEY_API_KEY, KEY_AI_OCR_MODEL_ID)
        "fallback" -> Triple("api_url_fallback", "api_key_fallback", "model_id_fallback")
        IMAGE_TYPE_TEXT_HEAVY -> Triple("api_url_text_heavy", "api_key_text_heavy", "model_id_text_heavy")
        IMAGE_TYPE_RICH_CONTENT -> Triple("api_url_rich_content", "api_key_rich_content", "model_id_rich_content")
        IMAGE_TYPE_SIMPLE_IMAGE -> Triple("api_url_simple_image", "api_key_simple_image", "model_id_simple_image")
        "backup_unified" -> Triple(KEY_FALLBACK_API_URL, KEY_FALLBACK_API_KEY, KEY_FALLBACK_MODEL_ID)
        "backup_fallback" -> Triple("backup_api_url_fallback", "backup_api_key_fallback", "backup_model_id_fallback")
        "backup_$IMAGE_TYPE_TEXT_HEAVY" -> Triple("backup_api_url_text_heavy", "backup_api_key_text_heavy", "backup_model_id_text_heavy")
        "backup_$IMAGE_TYPE_RICH_CONTENT" -> Triple("backup_api_url_rich_content", "backup_api_key_rich_content", "backup_model_id_rich_content")
        "backup_$IMAGE_TYPE_SIMPLE_IMAGE" -> Triple("backup_api_url_simple_image", "backup_api_key_simple_image", "backup_model_id_simple_image")
        else -> Triple("", "", "")
    }
}

/**
 * 获取API类型标题
 */
fun getApiTypeTitle(apiType: String): String {
    return when (apiType) {
        "unified" -> "统一API配置"
        "fallback" -> "分流API配置"
        IMAGE_TYPE_TEXT_HEAVY -> "文字密集型API配置"
        IMAGE_TYPE_RICH_CONTENT -> "富内容型API配置"
        IMAGE_TYPE_SIMPLE_IMAGE -> "简单图片型API配置"
        "backup_unified" -> "统一备用API配置"
        "backup_fallback" -> "分流备用API配置"
        "backup_$IMAGE_TYPE_TEXT_HEAVY" -> "文字密集型备用API配置"
        "backup_$IMAGE_TYPE_RICH_CONTENT" -> "富内容型备用API配置"
        "backup_$IMAGE_TYPE_SIMPLE_IMAGE" -> "简单图片型备用API配置"
        else -> "API配置"
    }
}

/**
 * API类型信息
 */
data class ApiTypeInfo(
    val type: String,
    val title: String,
    val description: String,
    val icon: String
)
