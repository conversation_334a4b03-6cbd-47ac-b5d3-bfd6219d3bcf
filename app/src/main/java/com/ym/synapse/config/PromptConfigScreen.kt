package com.ym.synapse.config

import android.content.Context
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.ym.synapse.*
import com.ym.synapse.ui.animations.PredictiveBackContainer

/**
 * 提示词配置主界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PromptConfigScreen(
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    var currentScreen by remember { mutableStateOf("main") }
    var selectedPromptType by remember { mutableStateOf("") }

    // 处理系统返回键
    androidx.activity.compose.BackHandler(enabled = currentScreen == "edit") {
        currentScreen = "main"
    }

    // 处理系统返回键
    androidx.activity.compose.BackHandler(enabled = currentScreen == "edit") {
        currentScreen = "main"
    }

    // 使用预测性返回容器包装AnimatedContent
    PredictiveBackContainer(
        onBackGesture = {
            if (currentScreen == "edit") {
                currentScreen = "main"
            } else {
                onBackClick()
            }
        },
        enabled = true,
        modifier = Modifier.fillMaxSize()
    ) {
        // 使用AnimatedContent实现原生滑动动画
        AnimatedContent(
            targetState = currentScreen,
            transitionSpec = {
                if (targetState == "edit") {
                    // 进入编辑页面：从右侧滑入
                    slideInHorizontally(
                        initialOffsetX = { fullWidth -> fullWidth },
                        animationSpec = tween(300, easing = FastOutSlowInEasing)
                    ) + fadeIn(
                        animationSpec = tween(300)
                    ) togetherWith slideOutHorizontally(
                        targetOffsetX = { fullWidth -> -fullWidth },
                        animationSpec = tween(300, easing = FastOutSlowInEasing)
                    ) + fadeOut(
                        animationSpec = tween(300)
                    )
                } else {
                    // 返回主页面：从左侧滑入
                    slideInHorizontally(
                        initialOffsetX = { fullWidth -> -fullWidth },
                        animationSpec = tween(300, easing = FastOutSlowInEasing)
                    ) + fadeIn(
                        animationSpec = tween(300)
                    ) togetherWith slideOutHorizontally(
                        targetOffsetX = { fullWidth -> fullWidth },
                        animationSpec = tween(300, easing = FastOutSlowInEasing)
                    ) + fadeOut(
                        animationSpec = tween(300)
                    )
                }
            },
            label = "prompt_config_navigation"
        ) { screen ->
            when (screen) {
                "main" -> PromptConfigMainScreen(
                    onBackClick = onBackClick,
                    onPromptTypeClick = { promptType ->
                        selectedPromptType = promptType
                        currentScreen = "edit"
                    }
                )
                "edit" -> PromptEditScreen(
                    promptType = selectedPromptType,
                    onBackClick = {
                        currentScreen = "main"
                    }
                )
            }
        }
    }
}

/**
 * 提示词配置主界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PromptConfigMainScreen(
    onBackClick: () -> Unit,
    onPromptTypeClick: (String) -> Unit
) {
    val context = LocalContext.current
    val sharedPreferences = remember {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    // 提示词类型列表
    val promptTypes = listOf(
        PromptTypeInfo(
            type = "fallback",
            title = "分流提示词",
            description = "分流API使用的提示词模板",
            icon = "🔄"
        ),
        PromptTypeInfo(
            type = IMAGE_TYPE_TEXT_HEAVY,
            title = "文字密集型",
            description = "处理包含大量文字的图片",
            icon = "📄"
        ),
        PromptTypeInfo(
            type = IMAGE_TYPE_RICH_CONTENT,
            title = "富内容型",
            description = "处理包含文字和图像混合内容的图片",
            icon = "🖼️"
        ),
        PromptTypeInfo(
            type = IMAGE_TYPE_SIMPLE_IMAGE,
            title = "简单图片型",
            description = "处理简单图像内容的图片",
            icon = "📸"
        )
    )

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("提示词设置") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            item {
                // 说明卡片
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "提示词管理",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "管理不同类型图片的AI分析提示词。点击卡片进入编辑界面。",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            items(promptTypes) { promptType ->
                PromptTypeCard(
                    promptType = promptType,
                    onClick = { onPromptTypeClick(promptType.type) }
                )
            }
        }
    }
}

/**
 * 提示词类型卡片
 */
@Composable
fun PromptTypeCard(
    promptType: PromptTypeInfo,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        onClick = onClick
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标
            Text(
                text = promptType.icon,
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(end = 16.dp)
            )

            // 内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = promptType.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = promptType.description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // 编辑图标
            Icon(
                imageVector = Icons.Default.Edit,
                contentDescription = "编辑",
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
}

/**
 * 提示词编辑界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PromptEditScreen(
    promptType: String,
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    val sharedPreferences = remember {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    // 获取当前提示词内容
    val currentPrompt = remember(promptType) {
        when (promptType) {
            "fallback" -> sharedPreferences.getString(KEY_AI_OCR_PROMPT, DEFAULT_AI_OCR_PROMPT) ?: DEFAULT_AI_OCR_PROMPT
            IMAGE_TYPE_TEXT_HEAVY -> sharedPreferences.getString(KEY_TEXT_HEAVY_PROMPT, DEFAULT_TEXT_HEAVY_PROMPT) ?: DEFAULT_TEXT_HEAVY_PROMPT
            IMAGE_TYPE_RICH_CONTENT -> sharedPreferences.getString(KEY_RICH_CONTENT_PROMPT, DEFAULT_RICH_CONTENT_PROMPT) ?: DEFAULT_RICH_CONTENT_PROMPT
            IMAGE_TYPE_SIMPLE_IMAGE -> sharedPreferences.getString(KEY_SIMPLE_IMAGE_PROMPT, DEFAULT_SIMPLE_IMAGE_PROMPT) ?: DEFAULT_SIMPLE_IMAGE_PROMPT
            else -> ""
        }
    }

    var promptText by remember { mutableStateOf(currentPrompt) }

    // 获取标题
    val title = when (promptType) {
        "fallback" -> "分流提示词"
        IMAGE_TYPE_TEXT_HEAVY -> "文字密集型提示词"
        IMAGE_TYPE_RICH_CONTENT -> "富内容型提示词"
        IMAGE_TYPE_SIMPLE_IMAGE -> "简单图片型提示词"
        else -> "提示词编辑"
    }

    // 自动保存函数
    val autoSave = {
        val key = when (promptType) {
            "fallback" -> KEY_AI_OCR_PROMPT
            IMAGE_TYPE_TEXT_HEAVY -> KEY_TEXT_HEAVY_PROMPT
            IMAGE_TYPE_RICH_CONTENT -> KEY_RICH_CONTENT_PROMPT
            IMAGE_TYPE_SIMPLE_IMAGE -> KEY_SIMPLE_IMAGE_PROMPT
            else -> ""
        }
        if (key.isNotEmpty()) {
            sharedPreferences.edit().putString(key, promptText).apply()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(title) },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            // 说明文字
            Text(
                text = "编辑 $title",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Text(
                text = "修改后会自动保存，无需手动保存",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // 提示词输入框
            OutlinedTextField(
                value = promptText,
                onValueChange = {
                    promptText = it
                    autoSave()
                },
                label = { Text("提示词内容") },
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                placeholder = { Text("请输入提示词...") }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 重置按钮
            OutlinedButton(
                onClick = {
                    val defaultPrompt = when (promptType) {
                        "fallback" -> DEFAULT_AI_OCR_PROMPT
                        IMAGE_TYPE_TEXT_HEAVY -> DEFAULT_TEXT_HEAVY_PROMPT
                        IMAGE_TYPE_RICH_CONTENT -> DEFAULT_RICH_CONTENT_PROMPT
                        IMAGE_TYPE_SIMPLE_IMAGE -> DEFAULT_SIMPLE_IMAGE_PROMPT
                        else -> ""
                    }
                    promptText = defaultPrompt
                    autoSave()
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("重置为默认提示词")
            }
        }
    }
}

/**
 * 提示词类型信息
 */
data class PromptTypeInfo(
    val type: String,
    val title: String,
    val description: String,
    val icon: String
)
