package com.ym.synapse.ui.animations

import androidx.compose.animation.core.*
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch

/**
 * 预测性返回动画容器
 * 为内部导航提供类似原生Activity的预测性返回手势
 */
@Composable
fun PredictiveBackContainer(
    onBackGesture: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    content: @Composable () -> Unit
) {
    val density = LocalDensity.current
    val scope = rememberCoroutineScope()
    
    var isDragging by remember { mutableStateOf(false) }
    var dragOffset by remember { mutableFloatStateOf(0f) }
    
    // 动画值
    val animatedOffset by animateFloatAsState(
        targetValue = if (isDragging) dragOffset else 0f,
        animationSpec = if (isDragging) {
            tween(0) // 跟随手指，无动画
        } else {
            spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessMedium
            )
        },
        label = "drag_offset"
    )
    
    val animatedScale by animateFloatAsState(
        targetValue = if (isDragging) {
            // 根据拖拽距离计算缩放比例 (0.85 - 1.0)
            1f - (dragOffset / 800f).coerceIn(0f, 0.15f)
        } else {
            1f
        },
        animationSpec = if (isDragging) {
            tween(0)
        } else {
            spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessMedium
            )
        },
        label = "drag_scale"
    )
    
    val animatedCornerRadius by animateFloatAsState(
        targetValue = if (isDragging) {
            (dragOffset / 20f).coerceIn(0f, 16f)
        } else {
            0f
        },
        animationSpec = if (isDragging) {
            tween(0)
        } else {
            spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessMedium
            )
        },
        label = "corner_radius"
    )
    
    // 简化的手势处理 - 更容易触发
    val gestureModifier = if (enabled) {
        Modifier.pointerInput(Unit) {
            detectDragGestures(
                onDragStart = { offset ->
                    android.util.Log.d("PredictiveBack", "*** DRAG START *** at x=${offset.x}, screen width=${size.width}")
                    // 扩大检测区域到屏幕宽度的50%，更容易触发
                    if (offset.x <= size.width * 0.5f) {
                        isDragging = true
                        dragOffset = 0f
                        android.util.Log.d("PredictiveBack", "*** DRAG ENABLED ***")
                    } else {
                        android.util.Log.d("PredictiveBack", "Drag outside edge area")
                    }
                },
                onDragEnd = {
                    android.util.Log.d("PredictiveBack", "*** DRAG END *** isDragging=$isDragging, dragOffset=$dragOffset")
                    if (isDragging) {
                        scope.launch {
                            // 降低触发阈值到30dp，更容易触发
                            if (dragOffset > 30.dp.toPx()) {
                                android.util.Log.d("PredictiveBack", "*** TRIGGERING BACK GESTURE ***")
                                onBackGesture()
                            } else {
                                android.util.Log.d("PredictiveBack", "Not enough drag distance: $dragOffset < ${30.dp.toPx()}")
                            }
                            // 重置状态
                            isDragging = false
                            dragOffset = 0f
                        }
                    }
                },
                onDrag = { change, _ ->
                    if (isDragging) {
                        // 计算水平拖拽距离
                        val horizontalDrag = change.position.x - change.previousPosition.x
                        if (horizontalDrag > 0) {
                            // 只允许向右拖拽
                            dragOffset = (dragOffset + horizontalDrag).coerceAtLeast(0f)
                            android.util.Log.d("PredictiveBack", "*** DRAGGING *** offset=$dragOffset, horizontal=$horizontalDrag")
                        }
                    }
                }
            )
        }
    } else {
        Modifier
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .then(gestureModifier)
            .scale(animatedScale)
            .graphicsLayer {
                translationX = animatedOffset
                clip = true
                shape = androidx.compose.foundation.shape.RoundedCornerShape(animatedCornerRadius.dp)
            }
    ) {
        content()
    }
}
