package com.ym.synapse.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Send
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
// 删除动画相关的import

/**
 * 分析历史卡片组件
 * 从MainActivity迁移而来，用于显示分析记录
 */
@Composable
fun AnalysisHistoryCard(
    record: com.ym.synapse.data.AnalysisRecord,
    onCardClick: () -> Unit,
    onDeleteClick: () -> Unit,
    onSendToNotion: (() -> Unit)? = null
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onCardClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    // 显示标题
                    Text(
                        text = record.notionResult?.title ?: record.getAutoTitle(),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    // 标签行
                    if (record.notionResult?.tags?.isNotEmpty() == true) {
                        LazyRow(
                            horizontalArrangement = Arrangement.spacedBy(6.dp),
                            modifier = Modifier.padding(vertical = 2.dp)
                        ) {
                            items(record.notionResult.tags.take(3)) { tag ->
                                Surface(
                                    color = MaterialTheme.colorScheme.primaryContainer,
                                    shape = RoundedCornerShape(8.dp)
                                ) {
                                    Text(
                                        text = tag,
                                        style = MaterialTheme.typography.labelSmall,
                                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp),
                                        color = MaterialTheme.colorScheme.onPrimaryContainer
                                    )
                                }
                            }
                            if (record.notionResult.tags.size > 3) {
                                item {
                                    Surface(
                                        color = MaterialTheme.colorScheme.surfaceVariant,
                                        shape = RoundedCornerShape(8.dp)
                                    ) {
                                        Text(
                                            text = "+${record.notionResult.tags.size - 3}",
                                            style = MaterialTheme.typography.labelSmall,
                                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp),
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                }
                            }
                        }

                        Spacer(modifier = Modifier.height(4.dp))
                    }

                    // 时间和类型标签行
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = record.getFormattedTime(),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        // 类型标签
                        Surface(
                            color = when (record.imageType) {
                                "Text-Heavy" -> MaterialTheme.colorScheme.secondaryContainer
                                "Rich-Content" -> MaterialTheme.colorScheme.tertiaryContainer
                                "Simple-Image" -> MaterialTheme.colorScheme.errorContainer
                                else -> MaterialTheme.colorScheme.surfaceVariant
                            },
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Text(
                                text = when (record.imageType) {
                                    "Text-Heavy" -> "文字"
                                    "Rich-Content" -> "富内容"
                                    "Simple-Image" -> "图片"
                                    else -> record.imageType
                                },
                                style = MaterialTheme.typography.labelSmall,
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp),
                                color = when (record.imageType) {
                                    "Text-Heavy" -> MaterialTheme.colorScheme.onSecondaryContainer
                                    "Rich-Content" -> MaterialTheme.colorScheme.onTertiaryContainer
                                    "Simple-Image" -> MaterialTheme.colorScheme.onErrorContainer
                                    else -> MaterialTheme.colorScheme.onSurfaceVariant
                                }
                            )
                        }
                    }
                }

                Row {
                    // Notion按钮（如果有NotionAnalysisResult且提供了回调）
                    if (record.notionResult != null && onSendToNotion != null) {
                        IconButton(onClick = onSendToNotion) {
                            Icon(
                                imageVector = Icons.Default.Send,
                                contentDescription = "发送到Notion",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    }

                    IconButton(onClick = onDeleteClick) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 内容预览（不显示全部内容）
            val previewText = record.notionResult?.summary ?: record.getPreviewText()
            Text(
                text = if (previewText.length > 80) {
                    previewText.take(80) + "..."
                } else {
                    previewText
                },
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
