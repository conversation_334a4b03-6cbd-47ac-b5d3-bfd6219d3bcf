package com.ym.synapse.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsFocusedAsState
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.ym.synapse.ui.animations.AnimationUtils

// 删除未使用的AnimatedButton组件

// 删除未使用的AnimatedCard组件

// 删除未使用的AnimatedIconButton组件

// 删除未使用的AnimatedFloatingActionButton组件

// 删除未使用的AnimatedSwitch组件

/**
 * 动画增强的进度指示器
 */
@Composable
fun AnimatedCircularProgressIndicator(
    progress: Float,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary,
    strokeWidth: androidx.compose.ui.unit.Dp = 4.dp,
    trackColor: Color = MaterialTheme.colorScheme.surfaceVariant,
    strokeCap: androidx.compose.ui.graphics.StrokeCap = androidx.compose.ui.graphics.StrokeCap.Round,
) {
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(
            durationMillis = AnimationUtils.DURATION_LONG,
            easing = AnimationUtils.EaseInOutCubic
        ),
        label = "progress"
    )
    
    CircularProgressIndicator(
        progress = animatedProgress,
        modifier = modifier,
        color = color,
        strokeWidth = strokeWidth,
        trackColor = trackColor,
        strokeCap = strokeCap
    )
}

// 删除未使用的AnimatedTextField组件
