package com.ym.synapse.ui.components

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign

/**
 * 简化的文本显示组件（移除Markwon依赖以减小包体积）
 */
@Composable
fun MarkdownText(
    markdown: String,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onSurface
) {
    // 简化版本：直接显示文本，不处理Markdown格式
    Text(
        text = markdown,
        modifier = modifier,
        color = color,
        style = MaterialTheme.typography.bodyMedium,
        textAlign = TextAlign.Start
    )
}

/**
 * 检测文本是否包含 Markdown 格式
 */
fun isMarkdownContent(text: String): Boolean {
    val markdownPatterns = listOf(
        "^#{1,6}\\s+.+$".toRegex(RegexOption.MULTILINE), // 标题
        "\\*\\*.+?\\*\\*".toRegex(), // 粗体
        "\\*.+?\\*".toRegex(), // 斜体
        "^\\s*[-*+]\\s+.+$".toRegex(RegexOption.MULTILINE), // 列表
        "^\\s*\\d+\\.\\s+.+$".toRegex(RegexOption.MULTILINE), // 有序列表
        "```[\\s\\S]*?```".toRegex(), // 代码块
        "`[^`]+`".toRegex(), // 行内代码
        "^\\s*>\\s+.+$".toRegex(RegexOption.MULTILINE), // 引用
        "\\[.+?\\]\\(.+?\\)".toRegex(), // 链接
        "^\\s*\\|.+\\|\\s*$".toRegex(RegexOption.MULTILINE) // 表格
    )
    
    return markdownPatterns.any { pattern ->
        pattern.containsMatchIn(text)
    }
}

/**
 * 将普通文本转换为基本的 Markdown 格式
 */
fun enhanceTextWithMarkdown(text: String, imageType: String): String {
    return when (imageType) {
        "Text-Heavy" -> enhanceTextHeavyContent(text)
        "Rich-Content" -> enhanceRichContent(text)
        "Simple-Image" -> text // 简单图片保持原样
        else -> text
    }
}

/**
 * 增强 Text-Heavy 内容的 Markdown 格式
 */
private fun enhanceTextHeavyContent(text: String): String {
    var enhanced = text
    
    // 将看起来像标题的行转换为 Markdown 标题
    enhanced = enhanced.replace(
        "^([^\\n]{1,50})\\n\\n".toRegex(RegexOption.MULTILINE)
    ) { matchResult ->
        val title = matchResult.groupValues[1].trim()
        if (title.isNotEmpty() && !title.endsWith("。") && !title.endsWith(".")) {
            "# $title\n\n"
        } else {
            matchResult.value
        }
    }
    
    // 将编号列表格式化
    enhanced = enhanced.replace(
        "^(\\d+[\\.、])\\s*(.+)$".toRegex(RegexOption.MULTILINE)
    ) { matchResult ->
        val number = matchResult.groupValues[1]
        val content = matchResult.groupValues[2]
        "$number $content"
    }
    
    // 将代码块格式化
    if (enhanced.contains("class ") || enhanced.contains("function ") || 
        enhanced.contains("def ") || enhanced.contains("import ")) {
        enhanced = "```\n$enhanced\n```"
    }
    
    return enhanced
}

/**
 * 增强 Rich-Content 内容的 Markdown 格式
 */
private fun enhanceRichContent(text: String): String {
    var enhanced = text
    
    // 将数字信息格式化为列表
    enhanced = enhanced.replace(
        "^([^\\n]*?)：([^\\n]+)$".toRegex(RegexOption.MULTILINE)
    ) { matchResult ->
        val key = matchResult.groupValues[1].trim()
        val value = matchResult.groupValues[2].trim()
        "- **$key**: $value"
    }
    
    // 将主要内容类型等关键信息加粗
    enhanced = enhanced.replace(
        "(主要内容类型|关键信息|重要信息|数据统计)".toRegex()
    ) { matchResult ->
        "**${matchResult.value}**"
    }
    
    return enhanced
}
