package com.ym.synapse.ui.animations

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.IntOffset

/**
 * 动画工具类 - 提供简约优雅的动画效果
 */
object AnimationUtils {
    
    // 标准动画时长
    const val DURATION_SHORT = 200
    const val DURATION_MEDIUM = 300
    const val DURATION_LONG = 500
    
    // 缓动曲线
    val EaseInOutCubic = CubicBezierEasing(0.4f, 0.0f, 0.2f, 1.0f)
    val EaseOutBack = CubicBezierEasing(0.34f, 1.56f, 0.64f, 1.0f)
    val EaseInOutQuart = CubicBezierEasing(0.76f, 0.0f, 0.24f, 1.0f)
    
    /**
     * 淡入淡出动画
     */
    fun fadeInOut(
        visible: Boolean,
        duration: Int = DURATION_MEDIUM,
        delay: Int = 0
    ): AnimationSpec<Float> {
        return tween(
            durationMillis = duration,
            delayMillis = delay,
            easing = EaseInOutCubic
        )
    }
    
    /**
     * 缩放动画
     */
    fun scaleInOut(
        visible: Boolean,
        duration: Int = DURATION_MEDIUM,
        delay: Int = 0
    ): AnimationSpec<Float> {
        return spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        )
    }
    
    /**
     * 滑动动画
     */
    fun slideInOut(
        duration: Int = DURATION_MEDIUM
    ): AnimationSpec<IntOffset> {
        return tween(
            durationMillis = duration,
            easing = EaseInOutQuart
        )
    }
}

/**
 * 淡入动画修饰符
 */
@Composable
fun Modifier.fadeInAnimation(
    visible: Boolean,
    duration: Int = AnimationUtils.DURATION_MEDIUM,
    delay: Int = 0
): Modifier {
    val alpha by animateFloatAsState(
        targetValue = if (visible) 1f else 0f,
        animationSpec = AnimationUtils.fadeInOut(visible, duration, delay),
        label = "fadeIn"
    )
    return this.alpha(alpha)
}

/**
 * 缩放动画修饰符
 */
@Composable
fun Modifier.scaleAnimation(
    visible: Boolean,
    duration: Int = AnimationUtils.DURATION_MEDIUM,
    delay: Int = 0
): Modifier {
    val scale by animateFloatAsState(
        targetValue = if (visible) 1f else 0.8f,
        animationSpec = AnimationUtils.scaleInOut(visible, duration, delay),
        label = "scale"
    )
    return this.scale(scale)
}

/**
 * 组合动画修饰符 - 淡入 + 缩放
 */
@Composable
fun Modifier.fadeScaleAnimation(
    visible: Boolean,
    duration: Int = AnimationUtils.DURATION_MEDIUM,
    delay: Int = 0
): Modifier {
    val alpha by animateFloatAsState(
        targetValue = if (visible) 1f else 0f,
        animationSpec = AnimationUtils.fadeInOut(visible, duration, delay),
        label = "fadeScale_alpha"
    )
    val scale by animateFloatAsState(
        targetValue = if (visible) 1f else 0.9f,
        animationSpec = AnimationUtils.scaleInOut(visible, duration, delay),
        label = "fadeScale_scale"
    )
    return this
        .alpha(alpha)
        .scale(scale)
}

/**
 * 悬浮动画修饰符
 */
@Composable
fun Modifier.hoverAnimation(
    hovered: Boolean,
    duration: Int = AnimationUtils.DURATION_SHORT
): Modifier {
    val elevation by animateFloatAsState(
        targetValue = if (hovered) 8f else 0f,
        animationSpec = tween(duration, easing = AnimationUtils.EaseInOutCubic),
        label = "hover_elevation"
    )
    val scale by animateFloatAsState(
        targetValue = if (hovered) 1.02f else 1f,
        animationSpec = tween(duration, easing = AnimationUtils.EaseInOutCubic),
        label = "hover_scale"
    )
    return this
        .scale(scale)
        .graphicsLayer {
            shadowElevation = elevation
        }
}

/**
 * 列表项动画
 */
@Composable
fun AnimatedListItem(
    visible: Boolean,
    index: Int,
    content: @Composable () -> Unit
) {
    val delay = (index * 50).coerceAtMost(300) // 最大延迟300ms
    
    Box(
        modifier = Modifier
            .fadeScaleAnimation(
                visible = visible,
                duration = AnimationUtils.DURATION_MEDIUM,
                delay = delay
            )
    ) {
        content()
    }
}



/**
 * 加载动画
 */
@Composable
fun LoadingAnimation(
    modifier: Modifier = Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "loading")
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = 1000,
                easing = LinearEasing
            )
        ),
        label = "loading_rotation"
    )
    
    Box(
        modifier = modifier
            .graphicsLayer {
                rotationZ = rotation
            },
        contentAlignment = Alignment.Center
    ) {
        // 这里可以放置加载图标
    }
}

/**
 * 脉冲动画
 */
@Composable
fun Modifier.pulseAnimation(
    enabled: Boolean = true,
    duration: Int = 1000
): Modifier {
    val infiniteTransition = rememberInfiniteTransition(label = "pulse")
    val scale by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = if (enabled) 1.1f else 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(duration, easing = AnimationUtils.EaseInOutCubic),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse_scale"
    )
    
    return if (enabled) {
        this.scale(scale)
    } else {
        this
    }
}

/**
 * 弹跳动画
 */
@Composable
fun Modifier.bounceAnimation(
    triggered: Boolean,
    duration: Int = AnimationUtils.DURATION_SHORT
): Modifier {
    val scale by animateFloatAsState(
        targetValue = if (triggered) 1.2f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "bounce"
    )
    
    return this.scale(scale)
}
