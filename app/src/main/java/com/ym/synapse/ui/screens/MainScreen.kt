package com.ym.synapse.ui.screens

import android.content.Context
import android.util.Log
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.*
import com.google.accompanist.permissions.*
import com.ym.synapse.utils.PermissionHelper
import com.ym.synapse.ui.navigation.AppScreen
import com.ym.synapse.ui.navigation.navigationItems
import com.ym.synapse.SettingsActivity

/**
 * 主屏幕组件
 * 从MainActivity迁移而来，处理应用的主要导航和状态管理
 */
@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun MainScreen() {
    val context = LocalContext.current
    val navController = rememberNavController()

    // 权限检测
    val permissionsToRequest = PermissionHelper.getPermissionRequestArray(context).toList()
    val multiplePermissionsState = rememberMultiplePermissionsState(permissionsToRequest)

    // 启动时检查权限
    LaunchedEffect(Unit) {
        if (!PermissionHelper.areAllRequiredPermissionsGranted(context)) {
            multiplePermissionsState.launchMultiplePermissionRequest()
        }
    }

    // 界面状态管理 - 从SharedPreferences恢复状态
    val sharedPreferences = remember {
        context.getSharedPreferences("app_state", Context.MODE_PRIVATE)
    }

    var showApiConfig by remember {
        mutableStateOf(sharedPreferences.getBoolean("show_api_config", false))
    }
    var showTutorial by remember {
        mutableStateOf(sharedPreferences.getBoolean("show_tutorial", false))
    }
    val allPermissionsGranted = PermissionHelper.areAllRequiredPermissionsGranted(context)
    val tutorialCompleted = sharedPreferences.getBoolean("tutorial_completed", false)

    // 保存状态到SharedPreferences
    LaunchedEffect(showApiConfig, showTutorial) {
        with(sharedPreferences.edit()) {
            putBoolean("show_api_config", showApiConfig)
            putBoolean("show_tutorial", showTutorial)
            apply()
        }
    }

    // 调试信息
    LaunchedEffect(allPermissionsGranted, showApiConfig, showTutorial) {
        Log.d("MainActivity", "allPermissionsGranted: $allPermissionsGranted, showApiConfig: $showApiConfig, showTutorial: $showTutorial")
    }

    // 决定显示哪个界面的逻辑
    when {
        tutorialCompleted -> {
            // 显示正常的应用界面 - 使用NavController进行主页和设置页面切换
            Scaffold(
                bottomBar = {
                    NavigationBar {
                        val navBackStackEntry by navController.currentBackStackEntryAsState()
                        val currentDestination = navBackStackEntry?.destination
                        navigationItems.forEach { screen ->
                            NavigationBarItem(
                                icon = { Icon(screen.icon, contentDescription = screen.label) },
                                label = { Text(screen.label) },
                                selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true,
                                onClick = {
                                    navController.navigate(screen.route) {
                                        popUpTo(navController.graph.findStartDestination().id) {
                                            saveState = true
                                        }
                                        launchSingleTop = true
                                        restoreState = true
                                    }
                                }
                            )
                        }
                    }
                }
            ) { innerPadding ->
                NavHost(navController, startDestination = AppScreen.Home.route, Modifier.padding(innerPadding)) {
                    composable(AppScreen.Home.route) { HomeScreen() }
                    composable(AppScreen.Settings.route) {
                        SettingsScreen(
                            onBackClick = {
                                // 退出应用
                                (context as? androidx.activity.ComponentActivity)?.finish()
                            }
                        )
                    }
                }
            }
        }
        showApiConfig -> {
            // 显示API配置界面
            Log.d("MainActivity", "Showing ApiConfigScreen")
            com.ym.synapse.ApiConfigScreen(
                onBackClick = { showApiConfig = false },
                onConfigComplete = {
                    showApiConfig = false
                    showTutorial = true
                }
            )
        }
        showTutorial -> {
            // 显示使用教程界面
            Log.d("MainActivity", "Showing TutorialScreen")
            com.ym.synapse.TutorialScreen(
                onBackClick = { showTutorial = false },
                onComplete = {
                    showTutorial = false
                    showApiConfig = false
                }
            )
        }
        !allPermissionsGranted -> {
            // 如果权限未授予，显示权限请求界面
            PermissionRequestScreen(
                multiplePermissionsState = multiplePermissionsState,
                onApiConfigClick = {
                    Log.d("MainActivity", "onApiConfigClick called")
                    showApiConfig = true
                }
            )
        }
        else -> {
            // 默认情况：如果没有其他条件匹配，显示API配置界面
            Log.d("MainActivity", "Default case: showing ApiConfigScreen")
            showApiConfig = true
            com.ym.synapse.ApiConfigScreen(
                onBackClick = { showApiConfig = false },
                onConfigComplete = {
                    showApiConfig = false
                    showTutorial = true
                }
            )
        }
    }
}
