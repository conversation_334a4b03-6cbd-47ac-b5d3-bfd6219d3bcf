package com.ym.synapse.data.repository

import android.content.Context
import android.content.SharedPreferences
import com.ym.synapse.DEFAULT_TEXT_HEAVY_PROMPT
import com.ym.synapse.DEFAULT_RICH_CONTENT_PROMPT
import com.ym.synapse.DEFAULT_SIMPLE_IMAGE_PROMPT
import com.ym.synapse.data.TagManager
import com.ym.synapse.KEY_NOTION_TOKEN
import com.ym.synapse.KEY_NOTION_DATABASE_ID
import com.ym.synapse.KEY_NOTION_ENABLED
import com.ym.synapse.KEY_NOTION_AUTO_SYNC
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 配置数据仓库
 * 统一管理应用的所有配置数据，提供响应式的配置更新
 */
class ConfigRepository private constructor(context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: ConfigRepository? = null
        
        fun getInstance(context: Context): ConfigRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ConfigRepository(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences("synapse_prefs", Context.MODE_PRIVATE)
    
    // 配置状态流
    private val _apiConfig = MutableStateFlow(getApiConfig())
    val apiConfig: Flow<ApiConfig> = _apiConfig.asStateFlow()

    private val _appState = MutableStateFlow(getAppState())
    val appState: Flow<AppState> = _appState.asStateFlow()

    private val _notionConfig = MutableStateFlow(getNotionConfig())
    val notionConfig: Flow<NotionConfig> = _notionConfig.asStateFlow()
    
    /**
     * API配置数据类
     */
    data class ApiConfig(
        val apiUrl: String = "",
        val apiKey: String = "",
        val modelId: String = "gpt-4o-mini",
        val fallbackEnabled: Boolean = false,
        val fallbackApiUrl: String = "",
        val fallbackApiKey: String = "",
        val fallbackModelId: String = "",
        val isConfigured: Boolean = false
    ) {
        fun isValid(): Boolean = apiUrl.isNotBlank() && apiKey.isNotBlank() && modelId.isNotBlank()
        fun isFallbackValid(): Boolean = fallbackApiUrl.isNotBlank() && fallbackApiKey.isNotBlank() && fallbackModelId.isNotBlank()
    }
    
    /**
     * 应用状态数据类
     */
    data class AppState(
        val showApiConfig: Boolean = false,
        val showTutorial: Boolean = false,
        val isFirstLaunch: Boolean = true,
        val hasCompletedSetup: Boolean = false
    )

    /**
     * Notion配置数据类
     */
    data class NotionConfig(
        val token: String = "",
        val databaseId: String = "",
        val enabled: Boolean = false,
        val autoSync: Boolean = false
    ) {
        fun isValid(): Boolean = token.isNotBlank() && databaseId.isNotBlank()
    }
    
    /**
     * 获取API配置
     */
    private fun getApiConfig(): ApiConfig {
        return ApiConfig(
            apiUrl = sharedPreferences.getString("api_url", "") ?: "",
            apiKey = sharedPreferences.getString("api_key", "") ?: "",
            modelId = sharedPreferences.getString("ai_ocr_model_id", "gpt-4o-mini") ?: "gpt-4o-mini",
            fallbackEnabled = sharedPreferences.getBoolean("fallback_enabled", false),
            fallbackApiUrl = sharedPreferences.getString("fallback_api_url", "") ?: "",
            fallbackApiKey = sharedPreferences.getString("fallback_api_key", "") ?: "",
            fallbackModelId = sharedPreferences.getString("fallback_model_id", "") ?: "",
            isConfigured = sharedPreferences.getBoolean("api_configured", false)
        )
    }
    
    /**
     * 获取应用状态
     */
    private fun getAppState(): AppState {
        return AppState(
            showApiConfig = sharedPreferences.getBoolean("show_api_config", false),
            showTutorial = sharedPreferences.getBoolean("show_tutorial", false),
            isFirstLaunch = sharedPreferences.getBoolean("is_first_launch", true),
            hasCompletedSetup = sharedPreferences.getBoolean("has_completed_setup", false)
        )
    }

    /**
     * 获取Notion配置
     */
    private fun getNotionConfig(): NotionConfig {
        return NotionConfig(
            token = sharedPreferences.getString(KEY_NOTION_TOKEN, "") ?: "",
            databaseId = sharedPreferences.getString(KEY_NOTION_DATABASE_ID, "") ?: "",
            enabled = sharedPreferences.getBoolean(KEY_NOTION_ENABLED, false),
            autoSync = sharedPreferences.getBoolean(KEY_NOTION_AUTO_SYNC, false)
        )
    }
    
    /**
     * 保存API配置
     */
    fun saveApiConfig(config: ApiConfig) {
        sharedPreferences.edit().apply {
            putString("api_url", config.apiUrl)
            putString("api_key", config.apiKey)
            putString("ai_ocr_model_id", config.modelId)
            putBoolean("fallback_enabled", config.fallbackEnabled)
            putString("fallback_api_url", config.fallbackApiUrl)
            putString("fallback_api_key", config.fallbackApiKey)
            putString("fallback_model_id", config.fallbackModelId)
            putBoolean("api_configured", config.isValid())
            apply()
        }
        _apiConfig.value = config.copy(isConfigured = config.isValid())
    }
    
    /**
     * 保存应用状态
     */
    fun saveAppState(state: AppState) {
        sharedPreferences.edit().apply {
            putBoolean("show_api_config", state.showApiConfig)
            putBoolean("show_tutorial", state.showTutorial)
            putBoolean("is_first_launch", state.isFirstLaunch)
            putBoolean("has_completed_setup", state.hasCompletedSetup)
            apply()
        }
        _appState.value = state
    }

    /**
     * 保存Notion配置
     */
    fun saveNotionConfig(config: NotionConfig) {
        sharedPreferences.edit().apply {
            putString(KEY_NOTION_TOKEN, config.token)
            putString(KEY_NOTION_DATABASE_ID, config.databaseId)
            putBoolean(KEY_NOTION_ENABLED, config.enabled)
            putBoolean(KEY_NOTION_AUTO_SYNC, config.autoSync)
            apply()
        }
        _notionConfig.value = config
    }
    
    /**
     * 更新API配置的单个字段
     */
    fun updateApiUrl(url: String) {
        val current = _apiConfig.value
        saveApiConfig(current.copy(apiUrl = url))
    }
    
    fun updateApiKey(key: String) {
        val current = _apiConfig.value
        saveApiConfig(current.copy(apiKey = key))
    }
    
    fun updateModelId(modelId: String) {
        val current = _apiConfig.value
        saveApiConfig(current.copy(modelId = modelId))
    }
    
    /**
     * 更新应用状态的单个字段
     */
    fun updateShowApiConfig(show: Boolean) {
        val current = _appState.value
        saveAppState(current.copy(showApiConfig = show))
    }
    
    fun updateShowTutorial(show: Boolean) {
        val current = _appState.value
        saveAppState(current.copy(showTutorial = show))
    }
    
    fun markSetupCompleted() {
        val current = _appState.value
        saveAppState(current.copy(
            hasCompletedSetup = true,
            isFirstLaunch = false,
            showApiConfig = false,
            showTutorial = false
        ))
    }

    /**
     * 更新Notion配置的单个字段
     */
    fun updateNotionToken(token: String) {
        val current = _notionConfig.value
        saveNotionConfig(current.copy(token = token))
    }

    fun updateNotionDatabaseId(databaseId: String) {
        val current = _notionConfig.value
        saveNotionConfig(current.copy(databaseId = databaseId))
    }

    fun updateNotionEnabled(enabled: Boolean) {
        val current = _notionConfig.value
        saveNotionConfig(current.copy(enabled = enabled))
    }

    fun updateNotionAutoSync(autoSync: Boolean) {
        val current = _notionConfig.value
        saveNotionConfig(current.copy(autoSync = autoSync))
    }
    
    /**
     * 获取模型配置
     * 根据图片类型返回对应的模型配置
     */
    fun getModelConfig(imageType: String, context: Context? = null): ModelConfig {
        // 生成标签建议文本
        val tagSuggestions = if (context != null) {
            val tagManager = TagManager.getInstance(context)
            tagManager.generateTagSuggestionText(imageType)
        } else {
            "暂无标签建议，请根据内容自由生成标签。"
        }

        val defaultPrompt = when (imageType) {
            "Text-Heavy" -> DEFAULT_TEXT_HEAVY_PROMPT.replace("{TAG_SUGGESTIONS}", tagSuggestions)
            "Rich-Content" -> DEFAULT_RICH_CONTENT_PROMPT.replace("{TAG_SUGGESTIONS}", tagSuggestions)
            "Simple-Image" -> DEFAULT_SIMPLE_IMAGE_PROMPT.replace("{TAG_SUGGESTIONS}", tagSuggestions)
            else -> "请分析这张图片的内容。"
        }

        return ModelConfig(
            modelId = sharedPreferences.getString("model_${imageType}_id", "gpt-4o-mini") ?: "gpt-4o-mini",
            prompt = sharedPreferences.getString("model_${imageType}_prompt", defaultPrompt) ?: defaultPrompt,
            temperature = sharedPreferences.getFloat("model_${imageType}_temperature", 0.7f),
            maxTokens = sharedPreferences.getInt("model_${imageType}_max_tokens", 1000)
        )
    }

    /**
     * 保存模型配置
     * 保存特定图片类型的模型配置
     */
    fun saveModelConfig(imageType: String, config: ModelConfig) {
        sharedPreferences.edit().apply {
            putString("model_${imageType}_id", config.modelId)
            putString("model_${imageType}_prompt", config.prompt)
            putFloat("model_${imageType}_temperature", config.temperature)
            putInt("model_${imageType}_max_tokens", config.maxTokens)
            apply()
        }
    }

    /**
     * 模型配置数据类
     */
    data class ModelConfig(
        val modelId: String,
        val prompt: String,
        val temperature: Float = 0.7f,
        val maxTokens: Int = 1000
    )
    
    /**
     * 重置所有配置
     */
    fun resetAllConfigs() {
        sharedPreferences.edit().clear().apply()
        _apiConfig.value = ApiConfig()
        _appState.value = AppState()
        _notionConfig.value = NotionConfig()
    }
    
    /**
     * 检查是否需要显示初始设置
     */
    fun shouldShowInitialSetup(): Boolean {
        val apiConfig = _apiConfig.value
        val appState = _appState.value
        
        return appState.isFirstLaunch || !apiConfig.isConfigured || !appState.hasCompletedSetup
    }
    
    /**
     * 获取当前配置摘要
     */
    fun getConfigSummary(): String {
        val apiConfig = _apiConfig.value
        val appState = _appState.value
        
        return buildString {
            appendLine("=== 配置摘要 ===")
            appendLine("API URL: ${if (apiConfig.apiUrl.isNotBlank()) "已配置" else "未配置"}")
            appendLine("API Key: ${if (apiConfig.apiKey.isNotBlank()) "已配置" else "未配置"}")
            appendLine("模型ID: ${apiConfig.modelId}")
            appendLine("配置完成: ${apiConfig.isConfigured}")
            appendLine("设置完成: ${appState.hasCompletedSetup}")
            appendLine("首次启动: ${appState.isFirstLaunch}")
        }
    }
}
