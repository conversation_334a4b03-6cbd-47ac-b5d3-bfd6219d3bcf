package com.ym.synapse.data

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.text.SimpleDateFormat
import java.util.*

/**
 * 分析历史记录数据类
 */
data class AnalysisRecord(
    val id: String = UUID.randomUUID().toString(),
    val imageType: String,
    val resultText: String,
    val imagePath: String,
    val timestamp: Long = System.currentTimeMillis(),
    val title: String = "", // 从结果文本中提取的简短标题
    val notionResult: NotionAnalysisResult? = null // 结构化的分析结果
) {
    /**
     * 获取格式化的时间
     */
    fun getFormattedTime(): String {
        val sdf = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
        return sdf.format(Date(timestamp))
    }
    
    /**
     * 获取简短的预览文本
     */
    fun getPreviewText(): String {
        return if (resultText.length > 100) {
            resultText.take(100) + "..."
        } else {
            resultText
        }
    }
    
    /**
     * 获取自动生成的标题
     */
    fun getAutoTitle(): String {
        // 优先使用NotionAnalysisResult中的标题
        if (notionResult != null && notionResult.title.isNotBlank()) {
            return notionResult.title
        }

        if (title.isNotEmpty()) return title

        // 检查resultText是否为null或空
        val safeResultText = resultText ?: ""
        if (safeResultText.isEmpty()) {
            return when (imageType) {
                "Text-Heavy" -> "文字识别结果"
                "Rich-Content" -> "富内容分析"
                "Simple-Image" -> "图片描述"
                else -> "分析结果"
            }
        }

        // 根据图片类型和内容自动生成标题
        return when (imageType) {
            "Text-Heavy" -> {
                // 尝试提取标题或第一行有意义的文本
                val lines = safeResultText.lines().filter { it.trim().isNotEmpty() }
                val firstLine = lines.firstOrNull()?.trim() ?: ""

                // 如果第一行看起来像标题（较短且没有标点符号结尾）
                if (firstLine.isNotEmpty() && firstLine.length <= 30 && !firstLine.endsWith("。") && !firstLine.endsWith(".")) {
                    firstLine
                } else if (firstLine.length > 25) {
                    firstLine.take(25) + "..."
                } else {
                    firstLine.ifEmpty { "文字识别结果" }
                }
            }
            "Rich-Content" -> {
                // 尝试从结果中提取关键信息作为标题
                val lines = safeResultText.lines()
                val titleLine = lines.find { it.contains("内容类型") || it.contains("主要内容") }
                titleLine?.substringAfter("：")?.substringAfter(":")?.trim()?.take(20) ?: "富内容分析"
            }
            "Simple-Image" -> {
                val firstLine = safeResultText.lines().firstOrNull()?.trim() ?: ""
                if (firstLine.length > 15) firstLine.take(15) + "..." else firstLine.ifEmpty { "图片描述" }
            }
            else -> "分析结果"
        }
    }
}

/**
 * 分析历史管理器
 */
object AnalysisHistoryManager {
    private const val PREFS_NAME = "analysis_history"
    private const val KEY_HISTORY_LIST = "history_list"
    private const val MAX_HISTORY_SIZE = 50 // 最多保存50条记录
    
    private val gson = Gson()
    
    /**
     * 添加新的分析记录
     */
    fun addRecord(context: Context, record: AnalysisRecord) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val currentList = getHistoryList(context).toMutableList()
        
        // 添加到列表开头（最新的在前面）
        currentList.add(0, record)
        
        // 限制列表大小
        if (currentList.size > MAX_HISTORY_SIZE) {
            currentList.removeAt(currentList.size - 1)
        }
        
        // 保存到SharedPreferences
        val json = gson.toJson(currentList)
        prefs.edit().putString(KEY_HISTORY_LIST, json).apply()
    }
    
    /**
     * 获取所有历史记录
     */
    fun getHistoryList(context: Context): List<AnalysisRecord> {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val json = prefs.getString(KEY_HISTORY_LIST, null) ?: return emptyList()
        
        return try {
            val type = object : TypeToken<List<AnalysisRecord>>() {}.type
            gson.fromJson<List<AnalysisRecord>>(json, type) ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 删除指定记录
     */
    fun deleteRecord(context: Context, recordId: String) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val currentList = getHistoryList(context).toMutableList()
        
        currentList.removeAll { it.id == recordId }
        
        val json = gson.toJson(currentList)
        prefs.edit().putString(KEY_HISTORY_LIST, json).apply()
    }
    
    /**
     * 清空所有历史记录
     */
    fun clearHistory(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().remove(KEY_HISTORY_LIST).apply()
    }
    
    /**
     * 根据类型筛选记录
     */
    fun getRecordsByType(context: Context, imageType: String): List<AnalysisRecord> {
        return getHistoryList(context).filter { it.imageType == imageType }
    }
}
