package com.ym.synapse

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.ym.synapse.ui.screens.SettingsScreen
import com.ym.synapse.ui.theme.SynapseTheme

class SettingsActivity : ComponentActivity() {
    
    companion object {
        fun createIntent(context: Context): Intent {
            return Intent(context, SettingsActivity::class.java)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        setContent {
            SynapseTheme {
                SettingsScreen(
                    onBackClick = { finish() }
                )
            }
        }
    }
}
