notifyStartActivity intent:Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.ym.synapse/.MainActivity bnds=[587,622][862,998] (has extras) }
notifyStartingWindowDrawn addAfterPrepareSurface for:ActivityRecord{b46ac03 u0 com.ym.synapse/.MainActivity
Error getting package info: com.ym.synapse
---------------------------- PROCESS STARTED (21135) for package com.ym.synapse ----------------------------
Access denied finding property "ro.odm.prev.product.name"
Unable to open '/data/app/~~69cJ5PVPcs2pJOVt-hw96A==/com.ym.synapse-vfQHdWJkjiZpSX1RtXjQLw==/base.dm': No such file or directory
Unable to open '/data/app/~~69cJ5PVPcs2pJOVt-hw96A==/com.ym.synapse-vfQHdWJkjiZpSX1RtXjQLw==/base.dm': No such file or directory
Odex status:io-error-no-oat filter:run-from-apk reason:unknown location:/data/app/~~69cJ5PVPcs2pJOVt-hw96A==/com.ym.synapse-vfQHdWJkjiZpSX1RtXjQLw==/base.apk cost:154
Configuring clns-7 for other apk /data/app/~~69cJ5PVPcs2pJOVt-hw96A==/com.ym.synapse-vfQHdWJkjiZpSX1RtXjQLw==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~69cJ5PVPcs2pJOVt-hw96A==/com.ym.synapse-vfQHdWJkjiZpSX1RtXjQLw==/lib/arm64:/data/app/~~69cJ5PVPcs2pJOVt-hw96A==/com.ym.synapse-vfQHdWJkjiZpSX1RtXjQLw==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.ym.synapse
Compat change id reported: 202956589; UID 10536; state: ENABLED
SynapseHook: Attempting to handle package: com.ym.synapse, Target: android
SynapseHook: Attempting to find and hook method: com.ym.synapse.hook.KeepAliveService#onDestroy() for package com.ym.synapse
SynapseHook: Successfully hooked com.ym.synapse.hook.KeepAliveService#onDestroy()
Currently set values for:
  angle_gl_driver_selection_pkgs=[]
  angle_gl_driver_selection_values=[]
Global.Settings values are invalid: number of packages: 0, number of values: 0
Updatable production driver is not supported on the device.
applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
Initializing WorkManager with default configuration.
Skipping component enablement for androidx.work.impl.background.systemjob.SystemJobService
Created SystemJobScheduler and enabled SystemJobService
The default process name was not specified.
Performing cleanup operations.
Load libSchedAssistJni.so using system ns (caller=/system/framework/oplus-framework.jar): ok
createInstance(64bit) : createExtendedFactory
Opened libSchedAssistExtImpl.so
sysOpen failed with error=Permission denied
open sharedFd failed with error=Permission denied
open sharedFd failed with error=Permission denied
open sharedFd failed with error=Permission denied
open sharedFd failed with error=No such file or directory
OplusGraphicsEventListener create now!
addEventListener success!
initOptConfig: pkg= com.ym.synapse, mOptEnable= true, mAnimAheadEnable= true, mInsertDefaultNum= 2, mFrameInsertEnableList= {}, mSCEnableList= []
Compat change id reported: 279646685; UID 10536; state: ENABLED
SupportApps size 9
createInstance(64bit) : createExtendedFactory
Opened libSchedAssistExtImpl.so
applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:0
searching for layers in '/data/app/~~69cJ5PVPcs2pJOVt-hw96A==/com.ym.synapse-vfQHdWJkjiZpSX1RtXjQLw==/lib/arm64'
searching for layers in '/data/app/~~69cJ5PVPcs2pJOVt-hw96A==/com.ym.synapse-vfQHdWJkjiZpSX1RtXjQLw==/base.apk!/lib/arm64-v8a'
I can't find colorx layer , let's enumerate colorx layers first.
Unknow feature:IOplusTextViewRTLUtilForUG
Load liboplushwui_jni.so using system ns (caller=/system/framework/oplus-framework.jar): ok
===== BEGIN DUMP OF OVERRIDDEN SETTINGS =====
===== END DUMP OF OVERRIDDEN SETTINGS =====
Compat change id reported: 309578419; UID 10536; state: ENABLED
get WMS extension: android.os.BinderProxy@edf6f8e
Fetching IMapper5 from QtiMapper5
Graphics lib is available
updateCurrentActivity: mCurrentActivityName=com.ym.synapse.MainActivity, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=2, isEnabledForScrollChanged=false
ComponentInfo{com.ym.synapse/com.ym.synapse.MainActivity} checkFinished=false 2
applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:0
Compat change id reported: 352594277; UID 10536; state: ENABLED
init sDebug to false, init sDebugIme to false, init sAlwaysOn to false
updateDebugToClass InputMethodManager.DEBUG = false
updateDebugToClass ImeFocusController.DEBUG = false
updateDebugToClass InsetsController.DEBUG = false
updateDebugToClass BaseInputConnection.DEBUG = false
Using previously loaded IMapper library.
QUALCOMM build          : fd94b6f0cf, Ibfa69e6ec0
Build Date              : 03/25/25
Shader Compiler Version : E031.47.18.27
Local Branch            : 
Remote Branch           : refs/tags/AU_LINUX_ANDROID_LA.VENDOR.15.4.0.11.00.00.1088.627
Remote Branch           : NONE
Reconstruct Branch      : NOTHING
Build Config            : S P 18.0.0 AArch64
Driver Path             : /vendor/lib64/hw/vulkan.adreno.so
Driver Version          : 0800.33
PFP                     : 0x01500077
ME                      : 0x01500014
Application Name    : android framework
Application Version : 0x00000000
Engine Name         : android framework
Engine Version      : 0x00000000
Api Version         : 0x00401000
Add to mViews: com.android.internal.policy.DecorView{1f486b4 I.E...... R.....ID 0,0-0,0 alpha=1.0 viewInfo = }[MainActivity],pkg= com.ym.synapse
createInstance(64bit) : createExtendedFactory
Opened libhwuiextimpl.so
setRusSupported enable 0
createInstance(64bit) : createExtendedFactory
Opened libSchedAssistExtImpl.so
onDisplayChanged -1 for VRI android.view.ViewRootImpl@16d4d9e
applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:0
[OplusViewMirrorManager] updateHostViewRootIfNeeded, not support android.view.ViewRootImpl@16d4d9e
Skipping component enablement for androidx.work.impl.background.systemalarm.RescheduleReceiver
wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=0, residentWS=false, scenario=0, bounds=Rect(0, 0 - 1440, 3168), relayoutAsync=false, flags=0, newFlags=1660944384, title=com.ym.synapse/com.ym.synapse.MainActivity
relayoutWindow result, sizeChanged:true, surfaceControlChanged:true, transformHintChanged:false, mSurfaceSize:Point(1440, 3168), mLastSurfaceSize:Point(0, 0), mWidth:-1, mHeight:-1, requestedWidth:1440, requestedHeight:3168, transformHint:0, installOrientation:0, displayRotation:0, isSurfaceValid:true, attr.flag:-2122252032, tmpFrames:ClientWindowFrames{frame=[0,0][1440,3168] display=[0,0][1440,3168] parentFrame=[0,0][0,0]}, relayoutAsync:false, mSyncSeqId:0
updateBlastSurfaceIfNeeded, surfaceSize:Point(1440, 3168), lastSurfaceSize:Point(0, 0), format:-1, blastBufferQueue:null
[Penguin]can't get PerfManager
[](id:528f00000000,api:0,p:-1,c:21135) connect: controlledByApp=false
[VRI[MainActivity]#0(BLAST Consumer)0](id:528f00000000,api:1,p:21135,c:21135) connect: api=1 producerControlledByApp=true
 setExtendedRangeBrightness sc=Surface(name=com.ym.synapse/com.ym.synapse.MainActivity)/@0xe49a96b,currentBufferRatio=1.0,desiredRatio=1.0
<ReadGpuID:366>: Reading chip ID through GSL
Cannot find perfservice
[VRI[MainActivity]#0](f:0,a:1) acquireNextBufferLocked size=1440x3168 mFrameNumber=1 applyTransaction=true mTimestamp=43510258857411(auto) mPendingTransactions.size=0 graphicBufferId=90774133800963 transform=0
Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true syncBuffer=false
createInstance(64bit) : createExtendedFactory
Opened libSchedAssistExtImpl.so
draw finished. seqId=0
initFrameRateConfig: 
	levels: [120, 60]
	thresholds: [200]
FRTCConfigManager: FRTC_CAPABILITY = 120, package name = com.ym.synapse, WINDOW_ANIMATION_SPEED_RATE = 10, SCROLL_ANIMATION_SPEED_RATE = 20, PACKAGE_ENABLE = false
init info: mPackageName = com.ym.synapse, mIsEnabled = false
Compiler allocated 7124KB to compile void android.view.ViewRootImpl.performTraversals()
allPermissionsGranted: true, showApiConfig: false, showTutorial: false
onFocusEvent true
Skipped 35 frames!  The application may be doing too much work on its main thread.
Shutting down VM
Crash unexpectedly: java.lang.NullPointerException: Attempt to invoke interface method 'int java.lang.CharSequence.length()' on a null object reference
	at com.ym.synapse.data.AnalysisRecord.getAutoTitle(AnalysisHistory.kt:50)
	at com.ym.synapse.ui.components.AnalysisHistoryCardKt$AnalysisHistoryCard$2.invoke(AnalysisHistoryCard.kt:49)
	at com.ym.synapse.ui.components.AnalysisHistoryCardKt$AnalysisHistoryCard$2.invoke(AnalysisHistoryCard.kt:36)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:118)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.material3.CardKt$Card$1.invoke(Card.kt:888)
	at androidx.compose.material3.CardKt$Card$1.invoke(Card.kt:96)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:109)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.material3.SurfaceKt$Surface$1.invoke(Surface.kt:126)
	at androidx.compose.material3.SurfaceKt$Surface$1.invoke(Surface.kt:108)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:109)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:380)
	at androidx.compose.material3.SurfaceKt.Surface-T9BRK9s(Surface.kt:105)
	at androidx.compose.material3.CardKt.Card(Card.kt:89)
	at com.ym.synapse.ui.components.AnalysisHistoryCardKt.AnalysisHistoryCard(AnalysisHistoryCard.kt:31)
	at com.ym.synapse.ui.screens.HomeScreenKt$HomeScreen$2$4$1$1$1.invoke(HomeScreen.kt:159)
	at com.ym.synapse.ui.screens.HomeScreenKt$HomeScreen$2$4$1$1$1.invoke(HomeScreen.kt:158)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:109)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at com.ym.synapse.ui.animations.AnimationUtilsKt.AnimatedListItem(AnimationUtils.kt:175)
	at com.ym.synapse.ui.screens.HomeScreenKt$HomeScreen$lambda$23$lambda$22$lambda$21$$inlined$itemsIndexed$default$3.invoke(LazyDsl.kt:434)
	at com.ym.synapse.ui.screens.HomeScreenKt$HomeScreen$lambda$23$lambda$22$lambda$21$$inlined$itemsIndexed$default$3.invoke(LazyDsl.kt:188)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:139)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.foundation.lazy.LazyListItemProviderImpl$Item$1.invoke(LazyListItemProvider.kt:79)
	at androidx.compose.foundation.lazy.LazyListItemProviderImpl$Item$1.invoke(LazyListItemProvider.kt:77)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:109)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:401)
	at androidx.compose.foundation.lazy.layout.LazyLayoutPinnableItemKt.LazyLayoutPinnableItem(LazyLayoutPinnableItem.kt:58)
	at androidx.compose.foundation.lazy.LazyListItemProviderImpl.Item(LazyListItemProvider.kt:77)
	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactoryKt$SkippableItem$1.invoke(LazyLayoutItemContentFactory.kt:136)
	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactoryKt$SkippableItem$1.invoke(LazyLayoutItemContentFactory.kt:135)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:109)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:401)
	at androidx.compose.runtime.saveable.SaveableStateHolderImpl.SaveableStateProvider(SaveableStateHolder.kt:85)
	at androidx.compose.foundation.lazy.layout.LazySaveableStateHolder.SaveableStateProvider(LazySaveableStateHolder.kt:85)
	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactoryKt.SkippableItem-JVlU9Rs(LazyLayoutItemContentFactory.kt:135)
	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactoryKt.access$SkippableItem-JVlU9Rs(LazyLayoutItemContentFactory.kt:1)
	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1.invoke(LazyLayoutItemContentFactory.kt:101)
	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1.invoke(LazyLayoutItemContentFactory.kt:91)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:109)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$subcompose$3$1$1.invoke(SubcomposeLayout.kt:1017)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$subcompose$3$1$1.invoke(SubcomposeLayout.kt:493)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:109)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.runtime.ActualJvm_jvmKt.invokeComposable(ActualJvm.jvm.kt:97)
	at androidx.compose.runtime.ComposerImpl.doCompose(Composer.kt:3595)
	at androidx.compose.runtime.ComposerImpl.composeContent$runtime_release(Composer.kt:3522)
	at androidx.compose.runtime.CompositionImpl.composeContent(Composition.kt:743)
	at androidx.compose.runtime.Recomposer.composeInitial$runtime_release(Recomposer.kt:1122)
	at androidx.compose.runtime.ComposerImpl$CompositionContextImpl.composeInitial$runtime_release(Composer.kt:3876)
	at androidx.compose.runtime.ComposerImpl$CompositionContextImpl.composeInitial$runtime_release(Composer.kt:3876)
	at androidx.compose.runtime.CompositionImpl.composeInitial(Composition.kt:649)
	at androidx.compose.runtime.CompositionImpl.setContent(Composition.kt:635)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcomposeInto(SubcomposeLayout.kt:516)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:488)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:479)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:463)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$Scope.subcompose(SubcomposeLayout.kt:895)
	at androidx.compose.foundation.lazy.layout.LazyLayoutMeasureScopeImpl.measure-0kLqBqw(LazyLayoutMeasureScope.kt:125)
	at androidx.compose.foundation.lazy.LazyListMeasuredItemProvider.getAndMeasure-0kLqBqw(LazyListMeasuredItemProvider.kt:55)
	at androidx.compose.foundation.lazy.LazyListMeasuredItemProvider.getAndMeasure-0kLqBqw$default(LazyListMeasuredItemProvider.kt:49)
	at androidx.compose.foundation.lazy.LazyListMeasureKt.measureLazyList-x0Ok8Vo(LazyListMeasure.kt:214)
	at androidx.compose.foundation.lazy.LazyListKt$rememberLazyListMeasurePolicy$1$1.invoke-0kLqBqw(LazyList.kt:326)
	at androidx.compose.foundation.lazy.LazyListKt$rememberLazyListMeasurePolicy$1$1.invoke(LazyList.kt:183)
	at androidx.compose.foundation.lazy.layout.LazyLayoutKt$LazyLayout$3$2$1.invoke-0kLqBqw(LazyLayout.kt:119)
	at androidx.compose.foundation.lazy.layout.LazyLayoutKt$LazyLayout$3$2$1.invoke(LazyLayout.kt:112)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$createMeasurePolicy$1.measure-3p2s80s(SubcomposeLayout.kt:725)
	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:135)
	at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier.measure-3p2s80s(GraphicsLayerModifier.kt:646)
	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:188)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:316)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:315)
	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:503)
	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:502)
	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:258)
	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:113)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:1782)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.access$performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:40)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.remeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:696)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.measure-BRTryo0(LayoutNodeLayoutDelegate.kt:672)
	at androidx.compose.foundation.layout.RowColumnMeasurePolicyKt.measure(RowColumnMeasurePolicy.kt:119)
	at androidx.compose.foundation.layout.RowColumnMeasurePolicyKt.measure$default(RowColumnMeasurePolicy.kt:74)
	at androidx.compose.foundation.layout.ColumnMeasurePolicy.measure-3p2s80s(Column.kt:208)
	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:135)
	at androidx.compose.foundation.layout.PaddingNode.measure-3p2s80s(Padding.kt:414)
	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:188)
	at androidx.compose.foundation.layout.FillNode.measure-3p2s80s(Size.kt:699)
	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:188)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:316)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:315)
	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2441)
	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:502)
	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:258)
	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:113)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:1782)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.access$performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:40)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.remeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:696)
	at androidx.compose.ui.node.LayoutNode.remeasure-_Sx5XlM$ui_release(LayoutNode.kt:1222)
	at androidx.compose.ui.node.LayoutNode.remeasure-_Sx5XlM$ui_release$default(LayoutNode.kt:1213)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.doRemeasure-sdFAvZA(MeasureAndLayoutDelegate.kt:369)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.remeasureAndRelayoutIfNeeded(MeasureAndLayoutDelegate.kt:566)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.onlyRemeasureIfScheduled(MeasureAndLayoutDelegate.kt:660)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.forceMeasureTheSubtreeInternal(MeasureAndLayoutDelegate.kt:686)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.forceMeasureTheSubtreeInternal(MeasureAndLayoutDelegate.kt:693)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.forceMeasureTheSubtreeInternal(MeasureAndLayoutDelegate.kt:693)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.forceMeasureTheSubtreeInternal(MeasureAndLayoutDelegate.kt:693)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.forceMeasureTheSubtreeInternal(MeasureAndLayoutDelegate.kt:693)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.forceMeasureTheSubtree(MeasureAndLayoutDelegate.kt:649)
	at androidx.compose.ui.platform.AndroidComposeView.forceMeasureTheSubtree(AndroidComposeView.android.kt:1299)
	at androidx.compose.ui.node.Owner.forceMeasureTheSubtree$default(Owner.kt:263)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.remeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:708)
	at androidx.compose.ui.node.LayoutNode.remeasure-_Sx5XlM$ui_release(LayoutNode.kt:1222)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.doRemeasure-sdFAvZA(MeasureAndLayoutDelegate.kt:367)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.remeasureOnly(MeasureAndLayoutDelegate.kt:622)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.measureOnly(MeasureAndLayoutDelegate.kt:420)
	at androidx.compose.ui.platform.AndroidComposeView.onMeasure(AndroidComposeView.android.kt:1370)
	at android.view.View.measure(View.java:28736)
	at androidx.compose.ui.platform.AbstractComposeView.internalOnMeasure$ui_release(ComposeView.android.kt:309)
	at androidx.compose.ui.platform.AbstractComposeView.onMeasure(ComposeView.android.kt:296)
	at android.view.View.measure(View.java:28736)
	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:7143)
	at android.widget.FrameLayout.onMeasure(FrameLayout.java:194)
	at android.view.View.measure(View.java:28736)
	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:7143)
	at android.widget.LinearLayout.measureChildBeforeLayout(LinearLayout.java:1608)
	at android.widget.LinearLayout.measureVertical(LinearLayout.java:878)
	at android.widget.LinearLayout.onMeasure(LinearLayout.java:721)
	at android.view.View.measure(View.java:28736)
	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:7143)
	at android.widget.FrameLayout.onMeasure(FrameLayout.java:194)
	at com.android.internal.policy.DecorView.onMeasure(DecorView.java:812)
	at android.view.View.measure(View.java:28736)
	at android.view.ViewRootImpl.performMeasure(ViewRootImpl.java:5424)
	at android.view.ViewRootImpl.measureHierarchy(ViewRootImpl.java:3657)
	at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:4016)
	at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:3315)
	at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:11529)
	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1638)
	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1647)
	at android.view.Choreographer.doCallbacks(Choreographer.java:1165)
	at android.view.Choreographer.doFrame(Choreographer.java:1052)
	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:1621)
	at android.os.Handler.handleCallback(Handler.java:995)
	at android.os.Handler.dispatchMessage(Handler.java:105)
	at android.os.Looper.loopOnce(Looper.java:288)
	at android.os.Looper.loop(Looper.java:393)
	at android.app.ActivityThread.main(ActivityThread.java:9535)
	at java.lang.reflect.Method.invoke(Native Method)
	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:600)
	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1005)
FATAL EXCEPTION: main
Process: com.ym.synapse, PID: 21135
java.lang.NullPointerException: Attempt to invoke interface method 'int java.lang.CharSequence.length()' on a null object reference
	at com.ym.synapse.data.AnalysisRecord.getAutoTitle(AnalysisHistory.kt:50)
	at com.ym.synapse.ui.components.AnalysisHistoryCardKt$AnalysisHistoryCard$2.invoke(AnalysisHistoryCard.kt:49)
	at com.ym.synapse.ui.components.AnalysisHistoryCardKt$AnalysisHistoryCard$2.invoke(AnalysisHistoryCard.kt:36)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:118)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.material3.CardKt$Card$1.invoke(Card.kt:888)
	at androidx.compose.material3.CardKt$Card$1.invoke(Card.kt:96)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:109)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.material3.SurfaceKt$Surface$1.invoke(Surface.kt:126)
	at androidx.compose.material3.SurfaceKt$Surface$1.invoke(Surface.kt:108)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:109)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:380)
	at androidx.compose.material3.SurfaceKt.Surface-T9BRK9s(Surface.kt:105)
	at androidx.compose.material3.CardKt.Card(Card.kt:89)
	at com.ym.synapse.ui.components.AnalysisHistoryCardKt.AnalysisHistoryCard(AnalysisHistoryCard.kt:31)
	at com.ym.synapse.ui.screens.HomeScreenKt$HomeScreen$2$4$1$1$1.invoke(HomeScreen.kt:159)
	at com.ym.synapse.ui.screens.HomeScreenKt$HomeScreen$2$4$1$1$1.invoke(HomeScreen.kt:158)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:109)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at com.ym.synapse.ui.animations.AnimationUtilsKt.AnimatedListItem(AnimationUtils.kt:175)
	at com.ym.synapse.ui.screens.HomeScreenKt$HomeScreen$lambda$23$lambda$22$lambda$21$$inlined$itemsIndexed$default$3.invoke(LazyDsl.kt:434)
	at com.ym.synapse.ui.screens.HomeScreenKt$HomeScreen$lambda$23$lambda$22$lambda$21$$inlined$itemsIndexed$default$3.invoke(LazyDsl.kt:188)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:139)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.foundation.lazy.LazyListItemProviderImpl$Item$1.invoke(LazyListItemProvider.kt:79)
	at androidx.compose.foundation.lazy.LazyListItemProviderImpl$Item$1.invoke(LazyListItemProvider.kt:77)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:109)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:401)
	at androidx.compose.foundation.lazy.layout.LazyLayoutPinnableItemKt.LazyLayoutPinnableItem(LazyLayoutPinnableItem.kt:58)
	at androidx.compose.foundation.lazy.LazyListItemProviderImpl.Item(LazyListItemProvider.kt:77)
	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactoryKt$SkippableItem$1.invoke(LazyLayoutItemContentFactory.kt:136)
	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactoryKt$SkippableItem$1.invoke(LazyLayoutItemContentFactory.kt:135)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:109)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.runtime.CompositionLocalKt.CompositionLocalProvider(CompositionLocal.kt:401)
	at androidx.compose.runtime.saveable.SaveableStateHolderImpl.SaveableStateProvider(SaveableStateHolder.kt:85)
	at androidx.compose.foundation.lazy.layout.LazySaveableStateHolder.SaveableStateProvider(LazySaveableStateHolder.kt:85)
	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactoryKt.SkippableItem-JVlU9Rs(LazyLayoutItemContentFactory.kt:135)
	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactoryKt.access$SkippableItem-JVlU9Rs(LazyLayoutItemContentFactory.kt:1)
	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1.invoke(LazyLayoutItemContentFactory.kt:101)
	at androidx.compose.foundation.lazy.layout.LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1.invoke(LazyLayoutItemContentFactory.kt:91)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:109)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$subcompose$3$1$1.invoke(SubcomposeLayout.kt:1017)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$subcompose$3$1$1.invoke(SubcomposeLayout.kt:493)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:109)
	at androidx.compose.runtime.internal.ComposableLambdaImpl.invoke(ComposableLambda.jvm.kt:35)
	at androidx.compose.runtime.ActualJvm_jvmKt.invokeComposable(ActualJvm.jvm.kt:97)
	at androidx.compose.runtime.ComposerImpl.doCompose(Composer.kt:3595)
	at androidx.compose.runtime.ComposerImpl.composeContent$runtime_release(Composer.kt:3522)
	at androidx.compose.runtime.CompositionImpl.composeContent(Composition.kt:743)
	at androidx.compose.runtime.Recomposer.composeInitial$runtime_release(Recomposer.kt:1122)
	at androidx.compose.runtime.ComposerImpl$CompositionContextImpl.composeInitial$runtime_release(Composer.kt:3876)
	at androidx.compose.runtime.ComposerImpl$CompositionContextImpl.composeInitial$runtime_release(Composer.kt:3876)
	at androidx.compose.runtime.CompositionImpl.composeInitial(Composition.kt:649)
	at androidx.compose.runtime.CompositionImpl.setContent(Composition.kt:635)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcomposeInto(SubcomposeLayout.kt:516)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:488)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:479)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState.subcompose(SubcomposeLayout.kt:463)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$Scope.subcompose(SubcomposeLayout.kt:895)
	at androidx.compose.foundation.lazy.layout.LazyLayoutMeasureScopeImpl.measure-0kLqBqw(LazyLayoutMeasureScope.kt:125)
	at androidx.compose.foundation.lazy.LazyListMeasuredItemProvider.getAndMeasure-0kLqBqw(LazyListMeasuredItemProvider.kt:55)
	at androidx.compose.foundation.lazy.LazyListMeasuredItemProvider.getAndMeasure-0kLqBqw$default(LazyListMeasuredItemProvider.kt:49)
	at androidx.compose.foundation.lazy.LazyListMeasureKt.measureLazyList-x0Ok8Vo(LazyListMeasure.kt:214)
	at androidx.compose.foundation.lazy.LazyListKt$rememberLazyListMeasurePolicy$1$1.invoke-0kLqBqw(LazyList.kt:326)
	at androidx.compose.foundation.lazy.LazyListKt$rememberLazyListMeasurePolicy$1$1.invoke(LazyList.kt:183)
	at androidx.compose.foundation.lazy.layout.LazyLayoutKt$LazyLayout$3$2$1.invoke-0kLqBqw(LazyLayout.kt:119)
	at androidx.compose.foundation.lazy.layout.LazyLayoutKt$LazyLayout$3$2$1.invoke(LazyLayout.kt:112)
	at androidx.compose.ui.layout.LayoutNodeSubcompositionsState$createMeasurePolicy$1.measure-3p2s80s(SubcomposeLayout.kt:725)
	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:135)
	at androidx.compose.ui.graphics.SimpleGraphicsLayerModifier.measure-3p2s80s(GraphicsLayerModifier.kt:646)
	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:188)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:316)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:315)
	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:503)
	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:502)
	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:258)
	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:113)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:1782)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.access$performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:40)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.remeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:696)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.measure-BRTryo0(LayoutNodeLayoutDelegate.kt:672)
	at androidx.compose.foundation.layout.RowColumnMeasurePolicyKt.measure(RowColumnMeasurePolicy.kt:119)
	at androidx.compose.foundation.layout.RowColumnMeasurePolicyKt.measure$default(RowColumnMeasurePolicy.kt:74)
	at androidx.compose.foundation.layout.ColumnMeasurePolicy.measure-3p2s80s(Column.kt:208)
	at androidx.compose.ui.node.InnerNodeCoordinator.measure-BRTryo0(InnerNodeCoordinator.kt:135)
	at androidx.compose.foundation.layout.PaddingNode.measure-3p2s80s(Padding.kt:414)
	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:188)
	at androidx.compose.foundation.layout.FillNode.measure-3p2s80s(Size.kt:699)
	at androidx.compose.ui.node.LayoutModifierNodeCoordinator.measure-BRTryo0(LayoutModifierNodeCoordinator.kt:188)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:316)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$performMeasureBlock$1.invoke(LayoutNodeLayoutDelegate.kt:315)
	at androidx.compose.runtime.snapshots.Snapshot$Companion.observe(Snapshot.kt:2441)
	at androidx.compose.runtime.snapshots.SnapshotStateObserver$ObservedScopeMap.observe(SnapshotStateObserver.kt:502)
	at androidx.compose.runtime.snapshots.SnapshotStateObserver.observeReads(SnapshotStateObserver.kt:258)
	at androidx.compose.ui.node.OwnerSnapshotObserver.observeReads$ui_release(OwnerSnapshotObserver.kt:133)
	at androidx.compose.ui.node.OwnerSnapshotObserver.observeMeasureSnapshotReads$ui_release(OwnerSnapshotObserver.kt:113)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:1782)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate.access$performMeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:40)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.remeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:696)
	at androidx.compose.ui.node.LayoutNode.remeasure-_Sx5XlM$ui_release(LayoutNode.kt:1222)
	at androidx.compose.ui.node.LayoutNode.remeasure-_Sx5XlM$ui_release$default(LayoutNode.kt:1213)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.doRemeasure-sdFAvZA(MeasureAndLayoutDelegate.kt:369)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.remeasureAndRelayoutIfNeeded(MeasureAndLayoutDelegate.kt:566)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.onlyRemeasureIfScheduled(MeasureAndLayoutDelegate.kt:660)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.forceMeasureTheSubtreeInternal(MeasureAndLayoutDelegate.kt:686)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.forceMeasureTheSubtreeInternal(MeasureAndLayoutDelegate.kt:693)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.forceMeasureTheSubtreeInternal(MeasureAndLayoutDelegate.kt:693)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.forceMeasureTheSubtreeInternal(MeasureAndLayoutDelegate.kt:693)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.forceMeasureTheSubtreeInternal(MeasureAndLayoutDelegate.kt:693)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.forceMeasureTheSubtree(MeasureAndLayoutDelegate.kt:649)
	at androidx.compose.ui.platform.AndroidComposeView.forceMeasureTheSubtree(AndroidComposeView.android.kt:1299)
	at androidx.compose.ui.node.Owner.forceMeasureTheSubtree$default(Owner.kt:263)
	at androidx.compose.ui.node.LayoutNodeLayoutDelegate$MeasurePassDelegate.remeasure-BRTryo0(LayoutNodeLayoutDelegate.kt:708)
	at androidx.compose.ui.node.LayoutNode.remeasure-_Sx5XlM$ui_release(LayoutNode.kt:1222)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.doRemeasure-sdFAvZA(MeasureAndLayoutDelegate.kt:367)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.remeasureOnly(MeasureAndLayoutDelegate.kt:622)
	at androidx.compose.ui.node.MeasureAndLayoutDelegate.measureOnly(MeasureAndLayoutDelegate.kt:420)
	at androidx.compose.ui.platform.AndroidComposeView.onMeasure(AndroidComposeView.android.kt:1370)
	at android.view.View.measure(View.java:28736)
	at androidx.compose.ui.platform.AbstractComposeView.internalOnMeasure$ui_release(ComposeView.android.kt:309)
	at androidx.compose.ui.platform.AbstractComposeView.onMeasure(ComposeView.android.kt:296)
	at android.view.View.measure(View.java:28736)
	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:7143)
	at android.widget.FrameLayout.onMeasure(FrameLayout.java:194)
	at android.view.View.measure(View.java:28736)
	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:7143)
	at android.widget.LinearLayout.measureChildBeforeLayout(LinearLayout.java:1608)
	at android.widget.LinearLayout.measureVertical(LinearLayout.java:878)
	at android.widget.LinearLayout.onMeasure(LinearLayout.java:721)
	at android.view.View.measure(View.java:28736)
	at android.view.ViewGroup.measureChildWithMargins(ViewGroup.java:7143)
	at android.widget.FrameLayout.onMeasure(FrameLayout.java:194)
	at com.android.internal.policy.DecorView.onMeasure(DecorView.java:812)
	at android.view.View.measure(View.java:28736)
	at android.view.ViewRootImpl.performMeasure(ViewRootImpl.java:5424)
	at android.view.ViewRootImpl.measureHierarchy(ViewRootImpl.java:3657)
	at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:4016)
	at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:3315)
	at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:11529)
	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1638)
	at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1647)
	at android.view.Choreographer.doCallbacks(Choreographer.java:1165)
	at android.view.Choreographer.doFrame(Choreographer.java:1052)
	at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:1621)
	at android.os.Handler.handleCallback(Handler.java:995)
	at android.os.Handler.dispatchMessage(Handler.java:105)
	at android.os.Looper.loopOnce(Looper.java:288)
	at android.os.Looper.loop(Looper.java:393)
	at android.app.ActivityThread.main(ActivityThread.java:9535)
	at java.lang.reflect.Method.invoke(Native Method)
	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:600)
	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1005)
KillAction: find invalid process: com.ym.synapse:21135:2
Sending signal. PID: 21135 SIG: 9
---------------------------- PROCESS ENDED (21135) for package com.ym.synapse ----------------------------
